{"best_metric": null, "best_model_checkpoint": null, "epoch": 3.552397868561279, "eval_steps": 500, "global_step": 1000, "is_hyper_param_search": false, "is_local_process_zero": true, "is_world_process_zero": true, "log_history": [{"epoch": 0.003552397868561279, "grad_norm": 1.7015180587768555, "learning_rate": 0.0001998220640569395, "loss": 0.688, "step": 1}, {"epoch": 0.035523978685612786, "grad_norm": 0.9029127955436707, "learning_rate": 0.000198220640569395, "loss": 0.5472, "step": 10}, {"epoch": 0.07104795737122557, "grad_norm": 0.8207581639289856, "learning_rate": 0.00019644128113879004, "loss": 0.5101, "step": 20}, {"epoch": 0.10657193605683836, "grad_norm": 0.8242790699005127, "learning_rate": 0.00019466192170818506, "loss": 0.4978, "step": 30}, {"epoch": 0.14209591474245115, "grad_norm": 0.8300735950469971, "learning_rate": 0.0001928825622775801, "loss": 0.4971, "step": 40}, {"epoch": 0.17761989342806395, "grad_norm": 0.8558394312858582, "learning_rate": 0.0001911032028469751, "loss": 0.4834, "step": 50}, {"epoch": 0.21314387211367672, "grad_norm": 0.8423389792442322, "learning_rate": 0.0001893238434163701, "loss": 0.4767, "step": 60}, {"epoch": 0.24866785079928952, "grad_norm": 0.8152554631233215, "learning_rate": 0.00018754448398576514, "loss": 0.4695, "step": 70}, {"epoch": 0.2841918294849023, "grad_norm": 0.8504221439361572, "learning_rate": 0.00018576512455516017, "loss": 0.4755, "step": 80}, {"epoch": 0.3197158081705151, "grad_norm": 0.8952543139457703, "learning_rate": 0.00018398576512455517, "loss": 0.4615, "step": 90}, {"epoch": 0.3552397868561279, "grad_norm": 0.8741092085838318, "learning_rate": 0.00018220640569395016, "loss": 0.4702, "step": 100}, {"epoch": 0.3907637655417407, "grad_norm": 0.9045767188072205, "learning_rate": 0.00018042704626334522, "loss": 0.4615, "step": 110}, {"epoch": 0.42628774422735344, "grad_norm": 0.8632111549377441, "learning_rate": 0.00017864768683274022, "loss": 0.441, "step": 120}, {"epoch": 0.46181172291296624, "grad_norm": 0.9584726095199585, "learning_rate": 0.00017686832740213524, "loss": 0.4631, "step": 130}, {"epoch": 0.49733570159857904, "grad_norm": 0.8534824252128601, "learning_rate": 0.00017508896797153024, "loss": 0.4439, "step": 140}, {"epoch": 0.5328596802841918, "grad_norm": 0.813208818435669, "learning_rate": 0.0001733096085409253, "loss": 0.4561, "step": 150}, {"epoch": 0.5683836589698046, "grad_norm": 0.8939151167869568, "learning_rate": 0.0001715302491103203, "loss": 0.4462, "step": 160}, {"epoch": 0.6039076376554174, "grad_norm": 0.8756887316703796, "learning_rate": 0.0001697508896797153, "loss": 0.4409, "step": 170}, {"epoch": 0.6394316163410302, "grad_norm": 0.8501383066177368, "learning_rate": 0.00016797153024911032, "loss": 0.4538, "step": 180}, {"epoch": 0.6749555950266429, "grad_norm": 0.8557757139205933, "learning_rate": 0.00016619217081850535, "loss": 0.4507, "step": 190}, {"epoch": 0.7104795737122558, "grad_norm": 0.9190842509269714, "learning_rate": 0.00016441281138790037, "loss": 0.4501, "step": 200}, {"epoch": 0.7460035523978685, "grad_norm": 0.8814340829849243, "learning_rate": 0.00016263345195729537, "loss": 0.4437, "step": 210}, {"epoch": 0.7815275310834814, "grad_norm": 0.8506101965904236, "learning_rate": 0.0001608540925266904, "loss": 0.4411, "step": 220}, {"epoch": 0.8170515097690941, "grad_norm": 0.9950603246688843, "learning_rate": 0.00015907473309608543, "loss": 0.4557, "step": 230}, {"epoch": 0.8525754884547069, "grad_norm": 0.8427396416664124, "learning_rate": 0.00015729537366548045, "loss": 0.4421, "step": 240}, {"epoch": 0.8880994671403197, "grad_norm": 0.8884470462799072, "learning_rate": 0.00015551601423487545, "loss": 0.4358, "step": 250}, {"epoch": 0.9236234458259325, "grad_norm": 0.8498861789703369, "learning_rate": 0.00015373665480427045, "loss": 0.4362, "step": 260}, {"epoch": 0.9591474245115453, "grad_norm": 0.8346251845359802, "learning_rate": 0.0001519572953736655, "loss": 0.4329, "step": 270}, {"epoch": 0.9946714031971581, "grad_norm": 0.9826127290725708, "learning_rate": 0.0001501779359430605, "loss": 0.444, "step": 280}, {"epoch": 1.030195381882771, "grad_norm": 0.9409159421920776, "learning_rate": 0.00014839857651245553, "loss": 0.3203, "step": 290}, {"epoch": 1.0657193605683837, "grad_norm": 0.9018016457557678, "learning_rate": 0.00014661921708185053, "loss": 0.3012, "step": 300}, {"epoch": 1.1012433392539964, "grad_norm": 0.9465328454971313, "learning_rate": 0.00014483985765124558, "loss": 0.2975, "step": 310}, {"epoch": 1.1367673179396092, "grad_norm": 0.8741580247879028, "learning_rate": 0.00014306049822064058, "loss": 0.2963, "step": 320}, {"epoch": 1.1722912966252221, "grad_norm": 0.9616216421127319, "learning_rate": 0.0001412811387900356, "loss": 0.3049, "step": 330}, {"epoch": 1.2078152753108349, "grad_norm": 1.0465084314346313, "learning_rate": 0.0001395017793594306, "loss": 0.3, "step": 340}, {"epoch": 1.2433392539964476, "grad_norm": 1.074040174484253, "learning_rate": 0.0001377224199288256, "loss": 0.3032, "step": 350}, {"epoch": 1.2788632326820604, "grad_norm": 1.0628745555877686, "learning_rate": 0.00013594306049822066, "loss": 0.2992, "step": 360}, {"epoch": 1.3143872113676731, "grad_norm": 0.963076114654541, "learning_rate": 0.00013416370106761566, "loss": 0.304, "step": 370}, {"epoch": 1.349911190053286, "grad_norm": 0.9926567077636719, "learning_rate": 0.00013238434163701069, "loss": 0.3013, "step": 380}, {"epoch": 1.3854351687388988, "grad_norm": 1.002286434173584, "learning_rate": 0.00013060498220640568, "loss": 0.3055, "step": 390}, {"epoch": 1.4209591474245116, "grad_norm": 0.9377162456512451, "learning_rate": 0.00012882562277580074, "loss": 0.3036, "step": 400}, {"epoch": 1.4564831261101243, "grad_norm": 1.0325545072555542, "learning_rate": 0.00012704626334519574, "loss": 0.3079, "step": 410}, {"epoch": 1.492007104795737, "grad_norm": 1.10152006149292, "learning_rate": 0.00012526690391459074, "loss": 0.3081, "step": 420}, {"epoch": 1.52753108348135, "grad_norm": 1.0291228294372559, "learning_rate": 0.00012348754448398576, "loss": 0.3043, "step": 430}, {"epoch": 1.5630550621669625, "grad_norm": 1.0268067121505737, "learning_rate": 0.0001217081850533808, "loss": 0.2981, "step": 440}, {"epoch": 1.5985790408525755, "grad_norm": 1.1189697980880737, "learning_rate": 0.00011992882562277582, "loss": 0.3047, "step": 450}, {"epoch": 1.6341030195381883, "grad_norm": 0.9710944890975952, "learning_rate": 0.00011814946619217081, "loss": 0.3048, "step": 460}, {"epoch": 1.669626998223801, "grad_norm": 1.1378909349441528, "learning_rate": 0.00011637010676156583, "loss": 0.3029, "step": 470}, {"epoch": 1.705150976909414, "grad_norm": 1.0213549137115479, "learning_rate": 0.00011459074733096087, "loss": 0.3052, "step": 480}, {"epoch": 1.7406749555950265, "grad_norm": 1.024008870124817, "learning_rate": 0.00011281138790035588, "loss": 0.3008, "step": 490}, {"epoch": 1.7761989342806395, "grad_norm": 1.0526665449142456, "learning_rate": 0.00011103202846975089, "loss": 0.2928, "step": 500}, {"epoch": 1.8117229129662522, "grad_norm": 1.0301263332366943, "learning_rate": 0.0001092526690391459, "loss": 0.3056, "step": 510}, {"epoch": 1.847246891651865, "grad_norm": 1.0209459066390991, "learning_rate": 0.00010747330960854095, "loss": 0.306, "step": 520}, {"epoch": 1.882770870337478, "grad_norm": 1.1364924907684326, "learning_rate": 0.00010569395017793596, "loss": 0.3045, "step": 530}, {"epoch": 1.9182948490230904, "grad_norm": 1.0859990119934082, "learning_rate": 0.00010391459074733096, "loss": 0.2992, "step": 540}, {"epoch": 1.9538188277087034, "grad_norm": 1.034498929977417, "learning_rate": 0.00010213523131672597, "loss": 0.2981, "step": 550}, {"epoch": 1.9893428063943162, "grad_norm": 1.0060198307037354, "learning_rate": 0.00010035587188612101, "loss": 0.3068, "step": 560}, {"epoch": 2.024866785079929, "grad_norm": 1.5455065965652466, "learning_rate": 9.857651245551602e-05, "loss": 0.2193, "step": 570}, {"epoch": 2.060390763765542, "grad_norm": 0.9255600571632385, "learning_rate": 9.679715302491104e-05, "loss": 0.179, "step": 580}, {"epoch": 2.0959147424511544, "grad_norm": 1.0063939094543457, "learning_rate": 9.501779359430606e-05, "loss": 0.1725, "step": 590}, {"epoch": 2.1314387211367674, "grad_norm": 1.1227489709854126, "learning_rate": 9.323843416370108e-05, "loss": 0.1752, "step": 600}, {"epoch": 2.1669626998223803, "grad_norm": 0.9597902894020081, "learning_rate": 9.14590747330961e-05, "loss": 0.1761, "step": 610}, {"epoch": 2.202486678507993, "grad_norm": 1.0818681716918945, "learning_rate": 8.96797153024911e-05, "loss": 0.1777, "step": 620}, {"epoch": 2.238010657193606, "grad_norm": 1.0507763624191284, "learning_rate": 8.790035587188611e-05, "loss": 0.1804, "step": 630}, {"epoch": 2.2735346358792183, "grad_norm": 1.1235977411270142, "learning_rate": 8.612099644128114e-05, "loss": 0.1821, "step": 640}, {"epoch": 2.3090586145648313, "grad_norm": 1.0945767164230347, "learning_rate": 8.434163701067615e-05, "loss": 0.1804, "step": 650}, {"epoch": 2.3445825932504443, "grad_norm": 1.118152379989624, "learning_rate": 8.256227758007118e-05, "loss": 0.1785, "step": 660}, {"epoch": 2.380106571936057, "grad_norm": 1.14769446849823, "learning_rate": 8.078291814946619e-05, "loss": 0.1804, "step": 670}, {"epoch": 2.4156305506216698, "grad_norm": 1.122255563735962, "learning_rate": 7.900355871886122e-05, "loss": 0.1821, "step": 680}, {"epoch": 2.4511545293072823, "grad_norm": 1.1691216230392456, "learning_rate": 7.722419928825623e-05, "loss": 0.1796, "step": 690}, {"epoch": 2.4866785079928952, "grad_norm": 1.1136704683303833, "learning_rate": 7.544483985765126e-05, "loss": 0.1769, "step": 700}, {"epoch": 2.522202486678508, "grad_norm": 1.1501438617706299, "learning_rate": 7.366548042704626e-05, "loss": 0.1819, "step": 710}, {"epoch": 2.5577264653641207, "grad_norm": 1.0902540683746338, "learning_rate": 7.188612099644128e-05, "loss": 0.1848, "step": 720}, {"epoch": 2.5932504440497337, "grad_norm": 1.0736850500106812, "learning_rate": 7.01067615658363e-05, "loss": 0.1806, "step": 730}, {"epoch": 2.6287744227353462, "grad_norm": 1.068372368812561, "learning_rate": 6.832740213523132e-05, "loss": 0.1812, "step": 740}, {"epoch": 2.664298401420959, "grad_norm": 1.1514421701431274, "learning_rate": 6.654804270462633e-05, "loss": 0.1808, "step": 750}, {"epoch": 2.699822380106572, "grad_norm": 1.099556803703308, "learning_rate": 6.476868327402136e-05, "loss": 0.1782, "step": 760}, {"epoch": 2.7353463587921847, "grad_norm": 1.1001543998718262, "learning_rate": 6.298932384341637e-05, "loss": 0.1797, "step": 770}, {"epoch": 2.7708703374777977, "grad_norm": 1.1669106483459473, "learning_rate": 6.12099644128114e-05, "loss": 0.1829, "step": 780}, {"epoch": 2.80639431616341, "grad_norm": 1.062537670135498, "learning_rate": 5.9430604982206406e-05, "loss": 0.1784, "step": 790}, {"epoch": 2.841918294849023, "grad_norm": 1.1131980419158936, "learning_rate": 5.765124555160143e-05, "loss": 0.1803, "step": 800}, {"epoch": 2.877442273534636, "grad_norm": 1.1185654401779175, "learning_rate": 5.587188612099644e-05, "loss": 0.1796, "step": 810}, {"epoch": 2.9129662522202486, "grad_norm": 1.110828161239624, "learning_rate": 5.4092526690391465e-05, "loss": 0.1809, "step": 820}, {"epoch": 2.9484902309058616, "grad_norm": 1.141533374786377, "learning_rate": 5.231316725978648e-05, "loss": 0.1802, "step": 830}, {"epoch": 2.984014209591474, "grad_norm": 1.1862740516662598, "learning_rate": 5.0533807829181504e-05, "loss": 0.1795, "step": 840}, {"epoch": 3.019538188277087, "grad_norm": 0.9796704053878784, "learning_rate": 4.875444839857651e-05, "loss": 0.136, "step": 850}, {"epoch": 3.0550621669626996, "grad_norm": 1.0639488697052002, "learning_rate": 4.697508896797153e-05, "loss": 0.1025, "step": 860}, {"epoch": 3.0905861456483126, "grad_norm": 0.9861094355583191, "learning_rate": 4.519572953736655e-05, "loss": 0.1025, "step": 870}, {"epoch": 3.1261101243339255, "grad_norm": 1.1397039890289307, "learning_rate": 4.341637010676157e-05, "loss": 0.1025, "step": 880}, {"epoch": 3.161634103019538, "grad_norm": 1.045083999633789, "learning_rate": 4.163701067615658e-05, "loss": 0.1012, "step": 890}, {"epoch": 3.197158081705151, "grad_norm": 1.0145237445831299, "learning_rate": 3.98576512455516e-05, "loss": 0.1024, "step": 900}, {"epoch": 3.232682060390764, "grad_norm": 0.9670788049697876, "learning_rate": 3.807829181494662e-05, "loss": 0.1021, "step": 910}, {"epoch": 3.2682060390763765, "grad_norm": 1.0408207178115845, "learning_rate": 3.629893238434164e-05, "loss": 0.1038, "step": 920}, {"epoch": 3.3037300177619895, "grad_norm": 1.0553535223007202, "learning_rate": 3.451957295373665e-05, "loss": 0.1039, "step": 930}, {"epoch": 3.339253996447602, "grad_norm": 1.1066817045211792, "learning_rate": 3.274021352313167e-05, "loss": 0.1026, "step": 940}, {"epoch": 3.374777975133215, "grad_norm": 1.0836055278778076, "learning_rate": 3.096085409252669e-05, "loss": 0.1035, "step": 950}, {"epoch": 3.410301953818828, "grad_norm": 1.0662622451782227, "learning_rate": 2.918149466192171e-05, "loss": 0.0993, "step": 960}, {"epoch": 3.4458259325044405, "grad_norm": 1.0555713176727295, "learning_rate": 2.7402135231316728e-05, "loss": 0.1036, "step": 970}, {"epoch": 3.4813499111900534, "grad_norm": 1.1204538345336914, "learning_rate": 2.5622775800711747e-05, "loss": 0.1022, "step": 980}, {"epoch": 3.516873889875666, "grad_norm": 1.0933459997177124, "learning_rate": 2.3843416370106764e-05, "loss": 0.0997, "step": 990}, {"epoch": 3.552397868561279, "grad_norm": 1.0452381372451782, "learning_rate": 2.2064056939501783e-05, "loss": 0.0998, "step": 1000}], "logging_steps": 10, "max_steps": 1124, "num_input_tokens_seen": 0, "num_train_epochs": 4, "save_steps": 100, "stateful_callbacks": {"TrainerControl": {"args": {"should_epoch_stop": false, "should_evaluate": false, "should_log": false, "should_save": true, "should_training_stop": false}, "attributes": {}}}, "total_flos": 7.040441782370304e+17, "train_batch_size": 16, "trial_name": null, "trial_params": null}