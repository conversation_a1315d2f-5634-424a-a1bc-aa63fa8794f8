{"best_metric": null, "best_model_checkpoint": null, "epoch": 3.552397868561279, "eval_steps": 500, "global_step": 1000, "is_hyper_param_search": false, "is_local_process_zero": true, "is_world_process_zero": true, "log_history": [{"epoch": 0.003552397868561279, "grad_norm": 1.7098368406295776, "learning_rate": 0.0001998220640569395, "loss": 0.7108, "step": 1}, {"epoch": 0.035523978685612786, "grad_norm": 0.9470831155776978, "learning_rate": 0.000198220640569395, "loss": 0.603, "step": 10}, {"epoch": 0.07104795737122557, "grad_norm": 0.9109015464782715, "learning_rate": 0.00019644128113879004, "loss": 0.5608, "step": 20}, {"epoch": 0.10657193605683836, "grad_norm": 0.8338848948478699, "learning_rate": 0.00019466192170818506, "loss": 0.5411, "step": 30}, {"epoch": 0.14209591474245115, "grad_norm": 0.8112273812294006, "learning_rate": 0.0001928825622775801, "loss": 0.5326, "step": 40}, {"epoch": 0.17761989342806395, "grad_norm": 0.8101668953895569, "learning_rate": 0.0001911032028469751, "loss": 0.5222, "step": 50}, {"epoch": 0.21314387211367672, "grad_norm": 0.8646085858345032, "learning_rate": 0.0001893238434163701, "loss": 0.5223, "step": 60}, {"epoch": 0.24866785079928952, "grad_norm": 0.9092680811882019, "learning_rate": 0.00018754448398576514, "loss": 0.5079, "step": 70}, {"epoch": 0.2841918294849023, "grad_norm": 0.8455390930175781, "learning_rate": 0.00018576512455516017, "loss": 0.5188, "step": 80}, {"epoch": 0.3197158081705151, "grad_norm": 0.8917500376701355, "learning_rate": 0.00018398576512455517, "loss": 0.4953, "step": 90}, {"epoch": 0.3552397868561279, "grad_norm": 0.8216926455497742, "learning_rate": 0.00018220640569395016, "loss": 0.4984, "step": 100}, {"epoch": 0.3907637655417407, "grad_norm": 0.8722490072250366, "learning_rate": 0.00018042704626334522, "loss": 0.5081, "step": 110}, {"epoch": 0.42628774422735344, "grad_norm": 0.8716620206832886, "learning_rate": 0.00017864768683274022, "loss": 0.5071, "step": 120}, {"epoch": 0.46181172291296624, "grad_norm": 0.8302916288375854, "learning_rate": 0.00017686832740213524, "loss": 0.4985, "step": 130}, {"epoch": 0.49733570159857904, "grad_norm": 0.9133330583572388, "learning_rate": 0.00017508896797153024, "loss": 0.5066, "step": 140}, {"epoch": 0.5328596802841918, "grad_norm": 0.8900353908538818, "learning_rate": 0.0001733096085409253, "loss": 0.4978, "step": 150}, {"epoch": 0.5683836589698046, "grad_norm": 0.868110716342926, "learning_rate": 0.0001715302491103203, "loss": 0.4881, "step": 160}, {"epoch": 0.6039076376554174, "grad_norm": 0.9674701690673828, "learning_rate": 0.0001697508896797153, "loss": 0.4842, "step": 170}, {"epoch": 0.6394316163410302, "grad_norm": 0.8244174718856812, "learning_rate": 0.00016797153024911032, "loss": 0.4884, "step": 180}, {"epoch": 0.6749555950266429, "grad_norm": 0.8981578350067139, "learning_rate": 0.00016619217081850535, "loss": 0.4926, "step": 190}, {"epoch": 0.7104795737122558, "grad_norm": 0.8525213599205017, "learning_rate": 0.00016441281138790037, "loss": 0.4688, "step": 200}, {"epoch": 0.7460035523978685, "grad_norm": 0.8503854274749756, "learning_rate": 0.00016263345195729537, "loss": 0.4812, "step": 210}, {"epoch": 0.7815275310834814, "grad_norm": 0.8378429412841797, "learning_rate": 0.0001608540925266904, "loss": 0.4752, "step": 220}, {"epoch": 0.8170515097690941, "grad_norm": 0.8433137536048889, "learning_rate": 0.00015907473309608543, "loss": 0.4838, "step": 230}, {"epoch": 0.8525754884547069, "grad_norm": 0.8625881671905518, "learning_rate": 0.00015729537366548045, "loss": 0.4842, "step": 240}, {"epoch": 0.8880994671403197, "grad_norm": 0.8446395993232727, "learning_rate": 0.00015551601423487545, "loss": 0.4753, "step": 250}, {"epoch": 0.9236234458259325, "grad_norm": 0.8802857995033264, "learning_rate": 0.00015373665480427045, "loss": 0.4746, "step": 260}, {"epoch": 0.9591474245115453, "grad_norm": 0.8830151557922363, "learning_rate": 0.0001519572953736655, "loss": 0.463, "step": 270}, {"epoch": 0.9946714031971581, "grad_norm": 0.8942744135856628, "learning_rate": 0.0001501779359430605, "loss": 0.4623, "step": 280}, {"epoch": 1.030195381882771, "grad_norm": 0.8919193744659424, "learning_rate": 0.00014839857651245553, "loss": 0.3486, "step": 290}, {"epoch": 1.0657193605683837, "grad_norm": 0.9537543058395386, "learning_rate": 0.00014661921708185053, "loss": 0.3202, "step": 300}, {"epoch": 1.1012433392539964, "grad_norm": 0.9802684187889099, "learning_rate": 0.00014483985765124558, "loss": 0.3283, "step": 310}, {"epoch": 1.1367673179396092, "grad_norm": 0.9275827407836914, "learning_rate": 0.00014306049822064058, "loss": 0.3308, "step": 320}, {"epoch": 1.1722912966252221, "grad_norm": 0.9195408225059509, "learning_rate": 0.0001412811387900356, "loss": 0.3257, "step": 330}, {"epoch": 1.2078152753108349, "grad_norm": 1.0085550546646118, "learning_rate": 0.0001395017793594306, "loss": 0.3282, "step": 340}, {"epoch": 1.2433392539964476, "grad_norm": 1.0242048501968384, "learning_rate": 0.0001377224199288256, "loss": 0.3268, "step": 350}, {"epoch": 1.2788632326820604, "grad_norm": 1.0057722330093384, "learning_rate": 0.00013594306049822066, "loss": 0.3236, "step": 360}, {"epoch": 1.3143872113676731, "grad_norm": 0.9671093225479126, "learning_rate": 0.00013416370106761566, "loss": 0.3213, "step": 370}, {"epoch": 1.349911190053286, "grad_norm": 1.0108784437179565, "learning_rate": 0.00013238434163701069, "loss": 0.3268, "step": 380}, {"epoch": 1.3854351687388988, "grad_norm": 1.0571061372756958, "learning_rate": 0.00013060498220640568, "loss": 0.3376, "step": 390}, {"epoch": 1.4209591474245116, "grad_norm": 1.0160425901412964, "learning_rate": 0.00012882562277580074, "loss": 0.3305, "step": 400}, {"epoch": 1.4564831261101243, "grad_norm": 0.9912699460983276, "learning_rate": 0.00012704626334519574, "loss": 0.3259, "step": 410}, {"epoch": 1.492007104795737, "grad_norm": 0.987017035484314, "learning_rate": 0.00012526690391459074, "loss": 0.3305, "step": 420}, {"epoch": 1.52753108348135, "grad_norm": 1.0596911907196045, "learning_rate": 0.00012348754448398576, "loss": 0.3257, "step": 430}, {"epoch": 1.5630550621669625, "grad_norm": 1.0443439483642578, "learning_rate": 0.0001217081850533808, "loss": 0.3283, "step": 440}, {"epoch": 1.5985790408525755, "grad_norm": 1.0812616348266602, "learning_rate": 0.00011992882562277582, "loss": 0.3342, "step": 450}, {"epoch": 1.6341030195381883, "grad_norm": 1.0382633209228516, "learning_rate": 0.00011814946619217081, "loss": 0.3343, "step": 460}, {"epoch": 1.669626998223801, "grad_norm": 0.988219678401947, "learning_rate": 0.00011637010676156583, "loss": 0.3227, "step": 470}, {"epoch": 1.705150976909414, "grad_norm": 1.066563606262207, "learning_rate": 0.00011459074733096087, "loss": 0.3339, "step": 480}, {"epoch": 1.7406749555950265, "grad_norm": 1.0551421642303467, "learning_rate": 0.00011281138790035588, "loss": 0.3339, "step": 490}, {"epoch": 1.7761989342806395, "grad_norm": 1.0502190589904785, "learning_rate": 0.00011103202846975089, "loss": 0.3335, "step": 500}, {"epoch": 1.8117229129662522, "grad_norm": 1.050269603729248, "learning_rate": 0.0001092526690391459, "loss": 0.3333, "step": 510}, {"epoch": 1.847246891651865, "grad_norm": 0.9839392304420471, "learning_rate": 0.00010747330960854095, "loss": 0.3311, "step": 520}, {"epoch": 1.882770870337478, "grad_norm": 1.0307663679122925, "learning_rate": 0.00010569395017793596, "loss": 0.3292, "step": 530}, {"epoch": 1.9182948490230904, "grad_norm": 1.0771185159683228, "learning_rate": 0.00010391459074733096, "loss": 0.3332, "step": 540}, {"epoch": 1.9538188277087034, "grad_norm": 1.0356502532958984, "learning_rate": 0.00010213523131672597, "loss": 0.3231, "step": 550}, {"epoch": 1.9893428063943162, "grad_norm": 1.0004467964172363, "learning_rate": 0.00010035587188612101, "loss": 0.33, "step": 560}, {"epoch": 2.024866785079929, "grad_norm": 1.571163535118103, "learning_rate": 9.857651245551602e-05, "loss": 0.2373, "step": 570}, {"epoch": 2.060390763765542, "grad_norm": 1.0352084636688232, "learning_rate": 9.679715302491104e-05, "loss": 0.1914, "step": 580}, {"epoch": 2.0959147424511544, "grad_norm": 1.041084885597229, "learning_rate": 9.501779359430606e-05, "loss": 0.1907, "step": 590}, {"epoch": 2.1314387211367674, "grad_norm": 1.13198983669281, "learning_rate": 9.323843416370108e-05, "loss": 0.185, "step": 600}, {"epoch": 2.1669626998223803, "grad_norm": 1.097927451133728, "learning_rate": 9.14590747330961e-05, "loss": 0.1912, "step": 610}, {"epoch": 2.202486678507993, "grad_norm": 1.1864994764328003, "learning_rate": 8.96797153024911e-05, "loss": 0.1884, "step": 620}, {"epoch": 2.238010657193606, "grad_norm": 1.0409718751907349, "learning_rate": 8.790035587188611e-05, "loss": 0.1921, "step": 630}, {"epoch": 2.2735346358792183, "grad_norm": 1.0466163158416748, "learning_rate": 8.612099644128114e-05, "loss": 0.1934, "step": 640}, {"epoch": 2.3090586145648313, "grad_norm": 1.1255738735198975, "learning_rate": 8.434163701067615e-05, "loss": 0.1933, "step": 650}, {"epoch": 2.3445825932504443, "grad_norm": 1.1614435911178589, "learning_rate": 8.256227758007118e-05, "loss": 0.1966, "step": 660}, {"epoch": 2.380106571936057, "grad_norm": 1.1797360181808472, "learning_rate": 8.078291814946619e-05, "loss": 0.1917, "step": 670}, {"epoch": 2.4156305506216698, "grad_norm": 1.1379317045211792, "learning_rate": 7.900355871886122e-05, "loss": 0.1943, "step": 680}, {"epoch": 2.4511545293072823, "grad_norm": 1.117519497871399, "learning_rate": 7.722419928825623e-05, "loss": 0.1973, "step": 690}, {"epoch": 2.4866785079928952, "grad_norm": 1.1454559564590454, "learning_rate": 7.544483985765126e-05, "loss": 0.1938, "step": 700}, {"epoch": 2.522202486678508, "grad_norm": 1.1875724792480469, "learning_rate": 7.366548042704626e-05, "loss": 0.1933, "step": 710}, {"epoch": 2.5577264653641207, "grad_norm": 1.1696364879608154, "learning_rate": 7.188612099644128e-05, "loss": 0.1952, "step": 720}, {"epoch": 2.5932504440497337, "grad_norm": 1.2519680261611938, "learning_rate": 7.01067615658363e-05, "loss": 0.1975, "step": 730}, {"epoch": 2.6287744227353462, "grad_norm": 1.1261740922927856, "learning_rate": 6.832740213523132e-05, "loss": 0.1941, "step": 740}, {"epoch": 2.664298401420959, "grad_norm": 1.1740617752075195, "learning_rate": 6.654804270462633e-05, "loss": 0.1928, "step": 750}, {"epoch": 2.699822380106572, "grad_norm": 1.1992725133895874, "learning_rate": 6.476868327402136e-05, "loss": 0.1972, "step": 760}, {"epoch": 2.7353463587921847, "grad_norm": 1.0736769437789917, "learning_rate": 6.298932384341637e-05, "loss": 0.1922, "step": 770}, {"epoch": 2.7708703374777977, "grad_norm": 1.1533417701721191, "learning_rate": 6.12099644128114e-05, "loss": 0.195, "step": 780}, {"epoch": 2.80639431616341, "grad_norm": 1.1708712577819824, "learning_rate": 5.9430604982206406e-05, "loss": 0.1994, "step": 790}, {"epoch": 2.841918294849023, "grad_norm": 1.161681890487671, "learning_rate": 5.765124555160143e-05, "loss": 0.1931, "step": 800}, {"epoch": 2.877442273534636, "grad_norm": 1.106526494026184, "learning_rate": 5.587188612099644e-05, "loss": 0.1919, "step": 810}, {"epoch": 2.9129662522202486, "grad_norm": 1.1076582670211792, "learning_rate": 5.4092526690391465e-05, "loss": 0.1934, "step": 820}, {"epoch": 2.9484902309058616, "grad_norm": 1.1640019416809082, "learning_rate": 5.231316725978648e-05, "loss": 0.1948, "step": 830}, {"epoch": 2.984014209591474, "grad_norm": 1.2846680879592896, "learning_rate": 5.0533807829181504e-05, "loss": 0.195, "step": 840}, {"epoch": 3.019538188277087, "grad_norm": 0.9621680974960327, "learning_rate": 4.875444839857651e-05, "loss": 0.1459, "step": 850}, {"epoch": 3.0550621669626996, "grad_norm": 1.1167327165603638, "learning_rate": 4.697508896797153e-05, "loss": 0.1062, "step": 860}, {"epoch": 3.0905861456483126, "grad_norm": 1.0648863315582275, "learning_rate": 4.519572953736655e-05, "loss": 0.1077, "step": 870}, {"epoch": 3.1261101243339255, "grad_norm": 1.0195437669754028, "learning_rate": 4.341637010676157e-05, "loss": 0.1073, "step": 880}, {"epoch": 3.161634103019538, "grad_norm": 1.301053524017334, "learning_rate": 4.163701067615658e-05, "loss": 0.1084, "step": 890}, {"epoch": 3.197158081705151, "grad_norm": 1.0708881616592407, "learning_rate": 3.98576512455516e-05, "loss": 0.1068, "step": 900}, {"epoch": 3.232682060390764, "grad_norm": 1.1190094947814941, "learning_rate": 3.807829181494662e-05, "loss": 0.1086, "step": 910}, {"epoch": 3.2682060390763765, "grad_norm": 1.118607521057129, "learning_rate": 3.629893238434164e-05, "loss": 0.1078, "step": 920}, {"epoch": 3.3037300177619895, "grad_norm": 1.1143946647644043, "learning_rate": 3.451957295373665e-05, "loss": 0.1092, "step": 930}, {"epoch": 3.339253996447602, "grad_norm": 1.1658315658569336, "learning_rate": 3.274021352313167e-05, "loss": 0.1071, "step": 940}, {"epoch": 3.374777975133215, "grad_norm": 1.0844485759735107, "learning_rate": 3.096085409252669e-05, "loss": 0.1083, "step": 950}, {"epoch": 3.410301953818828, "grad_norm": 1.1068997383117676, "learning_rate": 2.918149466192171e-05, "loss": 0.1085, "step": 960}, {"epoch": 3.4458259325044405, "grad_norm": 1.152588963508606, "learning_rate": 2.7402135231316728e-05, "loss": 0.1101, "step": 970}, {"epoch": 3.4813499111900534, "grad_norm": 1.178946614265442, "learning_rate": 2.5622775800711747e-05, "loss": 0.1072, "step": 980}, {"epoch": 3.516873889875666, "grad_norm": 1.1360350847244263, "learning_rate": 2.3843416370106764e-05, "loss": 0.1062, "step": 990}, {"epoch": 3.552397868561279, "grad_norm": 1.0929982662200928, "learning_rate": 2.2064056939501783e-05, "loss": 0.1073, "step": 1000}], "logging_steps": 10, "max_steps": 1124, "num_input_tokens_seen": 0, "num_train_epochs": 4, "save_steps": 100, "stateful_callbacks": {"TrainerControl": {"args": {"should_epoch_stop": false, "should_evaluate": false, "should_log": false, "should_save": true, "should_training_stop": false}, "attributes": {}}}, "total_flos": 7.040441782370304e+17, "train_batch_size": 16, "trial_name": null, "trial_params": null}