{"best_metric": null, "best_model_checkpoint": null, "epoch": 0.7104795737122558, "eval_steps": 500, "global_step": 200, "is_hyper_param_search": false, "is_local_process_zero": true, "is_world_process_zero": true, "log_history": [{"epoch": 0.003552397868561279, "grad_norm": 1.7098368406295776, "learning_rate": 0.0001998220640569395, "loss": 0.7108, "step": 1}, {"epoch": 0.035523978685612786, "grad_norm": 0.9470831155776978, "learning_rate": 0.000198220640569395, "loss": 0.603, "step": 10}, {"epoch": 0.07104795737122557, "grad_norm": 0.9109015464782715, "learning_rate": 0.00019644128113879004, "loss": 0.5608, "step": 20}, {"epoch": 0.10657193605683836, "grad_norm": 0.8338848948478699, "learning_rate": 0.00019466192170818506, "loss": 0.5411, "step": 30}, {"epoch": 0.14209591474245115, "grad_norm": 0.8112273812294006, "learning_rate": 0.0001928825622775801, "loss": 0.5326, "step": 40}, {"epoch": 0.17761989342806395, "grad_norm": 0.8101668953895569, "learning_rate": 0.0001911032028469751, "loss": 0.5222, "step": 50}, {"epoch": 0.21314387211367672, "grad_norm": 0.8646085858345032, "learning_rate": 0.0001893238434163701, "loss": 0.5223, "step": 60}, {"epoch": 0.24866785079928952, "grad_norm": 0.9092680811882019, "learning_rate": 0.00018754448398576514, "loss": 0.5079, "step": 70}, {"epoch": 0.2841918294849023, "grad_norm": 0.8455390930175781, "learning_rate": 0.00018576512455516017, "loss": 0.5188, "step": 80}, {"epoch": 0.3197158081705151, "grad_norm": 0.8917500376701355, "learning_rate": 0.00018398576512455517, "loss": 0.4953, "step": 90}, {"epoch": 0.3552397868561279, "grad_norm": 0.8216926455497742, "learning_rate": 0.00018220640569395016, "loss": 0.4984, "step": 100}, {"epoch": 0.3907637655417407, "grad_norm": 0.8722490072250366, "learning_rate": 0.00018042704626334522, "loss": 0.5081, "step": 110}, {"epoch": 0.42628774422735344, "grad_norm": 0.8716620206832886, "learning_rate": 0.00017864768683274022, "loss": 0.5071, "step": 120}, {"epoch": 0.46181172291296624, "grad_norm": 0.8302916288375854, "learning_rate": 0.00017686832740213524, "loss": 0.4985, "step": 130}, {"epoch": 0.49733570159857904, "grad_norm": 0.9133330583572388, "learning_rate": 0.00017508896797153024, "loss": 0.5066, "step": 140}, {"epoch": 0.5328596802841918, "grad_norm": 0.8900353908538818, "learning_rate": 0.0001733096085409253, "loss": 0.4978, "step": 150}, {"epoch": 0.5683836589698046, "grad_norm": 0.868110716342926, "learning_rate": 0.0001715302491103203, "loss": 0.4881, "step": 160}, {"epoch": 0.6039076376554174, "grad_norm": 0.9674701690673828, "learning_rate": 0.0001697508896797153, "loss": 0.4842, "step": 170}, {"epoch": 0.6394316163410302, "grad_norm": 0.8244174718856812, "learning_rate": 0.00016797153024911032, "loss": 0.4884, "step": 180}, {"epoch": 0.6749555950266429, "grad_norm": 0.8981578350067139, "learning_rate": 0.00016619217081850535, "loss": 0.4926, "step": 190}, {"epoch": 0.7104795737122558, "grad_norm": 0.8525213599205017, "learning_rate": 0.00016441281138790037, "loss": 0.4688, "step": 200}], "logging_steps": 10, "max_steps": 1124, "num_input_tokens_seen": 0, "num_train_epochs": 4, "save_steps": 100, "stateful_callbacks": {"TrainerControl": {"args": {"should_epoch_stop": false, "should_evaluate": false, "should_log": false, "should_save": true, "should_training_stop": false}, "attributes": {}}}, "total_flos": 1.4091452153856e+17, "train_batch_size": 16, "trial_name": null, "trial_params": null}