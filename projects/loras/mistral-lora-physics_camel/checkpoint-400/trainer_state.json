{"best_metric": null, "best_model_checkpoint": null, "epoch": 1.4209591474245116, "eval_steps": 500, "global_step": 400, "is_hyper_param_search": false, "is_local_process_zero": true, "is_world_process_zero": true, "log_history": [{"epoch": 0.003552397868561279, "grad_norm": 1.7098368406295776, "learning_rate": 0.0001998220640569395, "loss": 0.7108, "step": 1}, {"epoch": 0.035523978685612786, "grad_norm": 0.9470831155776978, "learning_rate": 0.000198220640569395, "loss": 0.603, "step": 10}, {"epoch": 0.07104795737122557, "grad_norm": 0.9109015464782715, "learning_rate": 0.00019644128113879004, "loss": 0.5608, "step": 20}, {"epoch": 0.10657193605683836, "grad_norm": 0.8338848948478699, "learning_rate": 0.00019466192170818506, "loss": 0.5411, "step": 30}, {"epoch": 0.14209591474245115, "grad_norm": 0.8112273812294006, "learning_rate": 0.0001928825622775801, "loss": 0.5326, "step": 40}, {"epoch": 0.17761989342806395, "grad_norm": 0.8101668953895569, "learning_rate": 0.0001911032028469751, "loss": 0.5222, "step": 50}, {"epoch": 0.21314387211367672, "grad_norm": 0.8646085858345032, "learning_rate": 0.0001893238434163701, "loss": 0.5223, "step": 60}, {"epoch": 0.24866785079928952, "grad_norm": 0.9092680811882019, "learning_rate": 0.00018754448398576514, "loss": 0.5079, "step": 70}, {"epoch": 0.2841918294849023, "grad_norm": 0.8455390930175781, "learning_rate": 0.00018576512455516017, "loss": 0.5188, "step": 80}, {"epoch": 0.3197158081705151, "grad_norm": 0.8917500376701355, "learning_rate": 0.00018398576512455517, "loss": 0.4953, "step": 90}, {"epoch": 0.3552397868561279, "grad_norm": 0.8216926455497742, "learning_rate": 0.00018220640569395016, "loss": 0.4984, "step": 100}, {"epoch": 0.3907637655417407, "grad_norm": 0.8722490072250366, "learning_rate": 0.00018042704626334522, "loss": 0.5081, "step": 110}, {"epoch": 0.42628774422735344, "grad_norm": 0.8716620206832886, "learning_rate": 0.00017864768683274022, "loss": 0.5071, "step": 120}, {"epoch": 0.46181172291296624, "grad_norm": 0.8302916288375854, "learning_rate": 0.00017686832740213524, "loss": 0.4985, "step": 130}, {"epoch": 0.49733570159857904, "grad_norm": 0.9133330583572388, "learning_rate": 0.00017508896797153024, "loss": 0.5066, "step": 140}, {"epoch": 0.5328596802841918, "grad_norm": 0.8900353908538818, "learning_rate": 0.0001733096085409253, "loss": 0.4978, "step": 150}, {"epoch": 0.5683836589698046, "grad_norm": 0.868110716342926, "learning_rate": 0.0001715302491103203, "loss": 0.4881, "step": 160}, {"epoch": 0.6039076376554174, "grad_norm": 0.9674701690673828, "learning_rate": 0.0001697508896797153, "loss": 0.4842, "step": 170}, {"epoch": 0.6394316163410302, "grad_norm": 0.8244174718856812, "learning_rate": 0.00016797153024911032, "loss": 0.4884, "step": 180}, {"epoch": 0.6749555950266429, "grad_norm": 0.8981578350067139, "learning_rate": 0.00016619217081850535, "loss": 0.4926, "step": 190}, {"epoch": 0.7104795737122558, "grad_norm": 0.8525213599205017, "learning_rate": 0.00016441281138790037, "loss": 0.4688, "step": 200}, {"epoch": 0.7460035523978685, "grad_norm": 0.8503854274749756, "learning_rate": 0.00016263345195729537, "loss": 0.4812, "step": 210}, {"epoch": 0.7815275310834814, "grad_norm": 0.8378429412841797, "learning_rate": 0.0001608540925266904, "loss": 0.4752, "step": 220}, {"epoch": 0.8170515097690941, "grad_norm": 0.8433137536048889, "learning_rate": 0.00015907473309608543, "loss": 0.4838, "step": 230}, {"epoch": 0.8525754884547069, "grad_norm": 0.8625881671905518, "learning_rate": 0.00015729537366548045, "loss": 0.4842, "step": 240}, {"epoch": 0.8880994671403197, "grad_norm": 0.8446395993232727, "learning_rate": 0.00015551601423487545, "loss": 0.4753, "step": 250}, {"epoch": 0.9236234458259325, "grad_norm": 0.8802857995033264, "learning_rate": 0.00015373665480427045, "loss": 0.4746, "step": 260}, {"epoch": 0.9591474245115453, "grad_norm": 0.8830151557922363, "learning_rate": 0.0001519572953736655, "loss": 0.463, "step": 270}, {"epoch": 0.9946714031971581, "grad_norm": 0.8942744135856628, "learning_rate": 0.0001501779359430605, "loss": 0.4623, "step": 280}, {"epoch": 1.030195381882771, "grad_norm": 0.8919193744659424, "learning_rate": 0.00014839857651245553, "loss": 0.3486, "step": 290}, {"epoch": 1.0657193605683837, "grad_norm": 0.9537543058395386, "learning_rate": 0.00014661921708185053, "loss": 0.3202, "step": 300}, {"epoch": 1.1012433392539964, "grad_norm": 0.9802684187889099, "learning_rate": 0.00014483985765124558, "loss": 0.3283, "step": 310}, {"epoch": 1.1367673179396092, "grad_norm": 0.9275827407836914, "learning_rate": 0.00014306049822064058, "loss": 0.3308, "step": 320}, {"epoch": 1.1722912966252221, "grad_norm": 0.9195408225059509, "learning_rate": 0.0001412811387900356, "loss": 0.3257, "step": 330}, {"epoch": 1.2078152753108349, "grad_norm": 1.0085550546646118, "learning_rate": 0.0001395017793594306, "loss": 0.3282, "step": 340}, {"epoch": 1.2433392539964476, "grad_norm": 1.0242048501968384, "learning_rate": 0.0001377224199288256, "loss": 0.3268, "step": 350}, {"epoch": 1.2788632326820604, "grad_norm": 1.0057722330093384, "learning_rate": 0.00013594306049822066, "loss": 0.3236, "step": 360}, {"epoch": 1.3143872113676731, "grad_norm": 0.9671093225479126, "learning_rate": 0.00013416370106761566, "loss": 0.3213, "step": 370}, {"epoch": 1.349911190053286, "grad_norm": 1.0108784437179565, "learning_rate": 0.00013238434163701069, "loss": 0.3268, "step": 380}, {"epoch": 1.3854351687388988, "grad_norm": 1.0571061372756958, "learning_rate": 0.00013060498220640568, "loss": 0.3376, "step": 390}, {"epoch": 1.4209591474245116, "grad_norm": 1.0160425901412964, "learning_rate": 0.00012882562277580074, "loss": 0.3305, "step": 400}], "logging_steps": 10, "max_steps": 1124, "num_input_tokens_seen": 0, "num_train_epochs": 4, "save_steps": 100, "stateful_callbacks": {"TrainerControl": {"args": {"should_epoch_stop": false, "should_evaluate": false, "should_log": false, "should_save": true, "should_training_stop": false}, "attributes": {}}}, "total_flos": 2.816528999251968e+17, "train_batch_size": 16, "trial_name": null, "trial_params": null}