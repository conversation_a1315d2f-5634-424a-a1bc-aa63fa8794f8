{"best_metric": null, "best_model_checkpoint": null, "epoch": 4.0, "eval_steps": 500, "global_step": 396, "is_hyper_param_search": false, "is_local_process_zero": true, "is_world_process_zero": true, "log_history": [{"epoch": 0.010101010101010102, "grad_norm": 2.897902011871338, "learning_rate": 0.0001994949494949495, "loss": 2.3046, "step": 1}, {"epoch": 0.10101010101010101, "grad_norm": 1.061120629310608, "learning_rate": 0.00019494949494949494, "loss": 2.0453, "step": 10}, {"epoch": 0.20202020202020202, "grad_norm": 1.3418407440185547, "learning_rate": 0.0001898989898989899, "loss": 1.9454, "step": 20}, {"epoch": 0.30303030303030304, "grad_norm": 1.2335113286972046, "learning_rate": 0.00018484848484848484, "loss": 1.8869, "step": 30}, {"epoch": 0.40404040404040403, "grad_norm": 1.3091297149658203, "learning_rate": 0.0001797979797979798, "loss": 1.8021, "step": 40}, {"epoch": 0.5050505050505051, "grad_norm": 1.4886325597763062, "learning_rate": 0.00017474747474747476, "loss": 1.7895, "step": 50}, {"epoch": 0.6060606060606061, "grad_norm": 1.4276422262191772, "learning_rate": 0.00016969696969696972, "loss": 1.7236, "step": 60}, {"epoch": 0.7070707070707071, "grad_norm": 1.5758155584335327, "learning_rate": 0.00016464646464646465, "loss": 1.6185, "step": 70}, {"epoch": 0.8080808080808081, "grad_norm": 1.5279079675674438, "learning_rate": 0.0001595959595959596, "loss": 1.6437, "step": 80}, {"epoch": 0.9090909090909091, "grad_norm": 1.7647353410720825, "learning_rate": 0.00015454545454545454, "loss": 1.562, "step": 90}, {"epoch": 1.0101010101010102, "grad_norm": 2.1367971897125244, "learning_rate": 0.0001494949494949495, "loss": 1.4994, "step": 100}, {"epoch": 1.1111111111111112, "grad_norm": 2.33250093460083, "learning_rate": 0.00014444444444444444, "loss": 1.1541, "step": 110}, {"epoch": 1.2121212121212122, "grad_norm": 2.418940782546997, "learning_rate": 0.0001393939393939394, "loss": 1.0776, "step": 120}, {"epoch": 1.3131313131313131, "grad_norm": 2.603919744491577, "learning_rate": 0.00013434343434343436, "loss": 1.0823, "step": 130}, {"epoch": 1.4141414141414141, "grad_norm": 2.840055465698242, "learning_rate": 0.00012929292929292932, "loss": 1.0548, "step": 140}, {"epoch": 1.5151515151515151, "grad_norm": 2.690005302429199, "learning_rate": 0.00012424242424242425, "loss": 1.0021, "step": 150}, {"epoch": 1.6161616161616161, "grad_norm": 2.648704767227173, "learning_rate": 0.00011919191919191919, "loss": 0.954, "step": 160}, {"epoch": 1.7171717171717171, "grad_norm": 3.0394766330718994, "learning_rate": 0.00011414141414141415, "loss": 0.9306, "step": 170}, {"epoch": 1.8181818181818183, "grad_norm": 3.336500406265259, "learning_rate": 0.00010909090909090909, "loss": 0.8987, "step": 180}, {"epoch": 1.9191919191919191, "grad_norm": 2.857001543045044, "learning_rate": 0.00010404040404040405, "loss": 0.8868, "step": 190}, {"epoch": 2.0202020202020203, "grad_norm": 2.885561227798462, "learning_rate": 9.8989898989899e-05, "loss": 0.8222, "step": 200}, {"epoch": 2.121212121212121, "grad_norm": 3.566436290740967, "learning_rate": 9.393939393939395e-05, "loss": 0.4712, "step": 210}, {"epoch": 2.2222222222222223, "grad_norm": 3.1027793884277344, "learning_rate": 8.888888888888889e-05, "loss": 0.4827, "step": 220}, {"epoch": 2.323232323232323, "grad_norm": 2.9648356437683105, "learning_rate": 8.383838383838384e-05, "loss": 0.4398, "step": 230}, {"epoch": 2.4242424242424243, "grad_norm": 3.772846221923828, "learning_rate": 7.878787878787879e-05, "loss": 0.4475, "step": 240}, {"epoch": 2.525252525252525, "grad_norm": 3.492175579071045, "learning_rate": 7.373737373737373e-05, "loss": 0.4229, "step": 250}, {"epoch": 2.6262626262626263, "grad_norm": 3.874164581298828, "learning_rate": 6.86868686868687e-05, "loss": 0.3948, "step": 260}, {"epoch": 2.7272727272727275, "grad_norm": 4.261559009552002, "learning_rate": 6.363636363636364e-05, "loss": 0.4196, "step": 270}, {"epoch": 2.8282828282828283, "grad_norm": 3.6502771377563477, "learning_rate": 5.858585858585859e-05, "loss": 0.3606, "step": 280}, {"epoch": 2.929292929292929, "grad_norm": 4.155453205108643, "learning_rate": 5.353535353535354e-05, "loss": 0.3683, "step": 290}, {"epoch": 3.0303030303030303, "grad_norm": 2.358050584793091, "learning_rate": 4.848484848484849e-05, "loss": 0.3053, "step": 300}, {"epoch": 3.1313131313131315, "grad_norm": 3.010200262069702, "learning_rate": 4.343434343434344e-05, "loss": 0.2009, "step": 310}, {"epoch": 3.2323232323232323, "grad_norm": 2.5386440753936768, "learning_rate": 3.838383838383838e-05, "loss": 0.1806, "step": 320}, {"epoch": 3.3333333333333335, "grad_norm": 3.249920129776001, "learning_rate": 3.3333333333333335e-05, "loss": 0.2011, "step": 330}, {"epoch": 3.4343434343434343, "grad_norm": 2.6424648761749268, "learning_rate": 2.8282828282828282e-05, "loss": 0.1893, "step": 340}, {"epoch": 3.5353535353535355, "grad_norm": 3.160552740097046, "learning_rate": 2.3232323232323232e-05, "loss": 0.1648, "step": 350}, {"epoch": 3.6363636363636362, "grad_norm": 3.1822733879089355, "learning_rate": 1.8181818181818182e-05, "loss": 0.1854, "step": 360}, {"epoch": 3.7373737373737375, "grad_norm": 2.9233758449554443, "learning_rate": 1.3131313131313134e-05, "loss": 0.1706, "step": 370}, {"epoch": 3.8383838383838382, "grad_norm": 2.8263723850250244, "learning_rate": 8.080808080808082e-06, "loss": 0.1673, "step": 380}, {"epoch": 3.9393939393939394, "grad_norm": 2.0003182888031006, "learning_rate": 3.0303030303030305e-06, "loss": 0.159, "step": 390}], "logging_steps": 10, "max_steps": 396, "num_input_tokens_seen": 0, "num_train_epochs": 4, "save_steps": 100, "stateful_callbacks": {"TrainerControl": {"args": {"should_epoch_stop": false, "should_evaluate": false, "should_log": false, "should_save": true, "should_training_stop": true}, "attributes": {}}}, "total_flos": 2.741712023457792e+17, "train_batch_size": 16, "trial_name": null, "trial_params": null}