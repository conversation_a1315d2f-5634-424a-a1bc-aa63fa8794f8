{"best_metric": null, "best_model_checkpoint": null, "epoch": 2.0202020202020203, "eval_steps": 500, "global_step": 200, "is_hyper_param_search": false, "is_local_process_zero": true, "is_world_process_zero": true, "log_history": [{"epoch": 0.010101010101010102, "grad_norm": 2.897902011871338, "learning_rate": 0.0001994949494949495, "loss": 2.3046, "step": 1}, {"epoch": 0.10101010101010101, "grad_norm": 1.061120629310608, "learning_rate": 0.00019494949494949494, "loss": 2.0453, "step": 10}, {"epoch": 0.20202020202020202, "grad_norm": 1.3418407440185547, "learning_rate": 0.0001898989898989899, "loss": 1.9454, "step": 20}, {"epoch": 0.30303030303030304, "grad_norm": 1.2335113286972046, "learning_rate": 0.00018484848484848484, "loss": 1.8869, "step": 30}, {"epoch": 0.40404040404040403, "grad_norm": 1.3091297149658203, "learning_rate": 0.0001797979797979798, "loss": 1.8021, "step": 40}, {"epoch": 0.5050505050505051, "grad_norm": 1.4886325597763062, "learning_rate": 0.00017474747474747476, "loss": 1.7895, "step": 50}, {"epoch": 0.6060606060606061, "grad_norm": 1.4276422262191772, "learning_rate": 0.00016969696969696972, "loss": 1.7236, "step": 60}, {"epoch": 0.7070707070707071, "grad_norm": 1.5758155584335327, "learning_rate": 0.00016464646464646465, "loss": 1.6185, "step": 70}, {"epoch": 0.8080808080808081, "grad_norm": 1.5279079675674438, "learning_rate": 0.0001595959595959596, "loss": 1.6437, "step": 80}, {"epoch": 0.9090909090909091, "grad_norm": 1.7647353410720825, "learning_rate": 0.00015454545454545454, "loss": 1.562, "step": 90}, {"epoch": 1.0101010101010102, "grad_norm": 2.1367971897125244, "learning_rate": 0.0001494949494949495, "loss": 1.4994, "step": 100}, {"epoch": 1.1111111111111112, "grad_norm": 2.33250093460083, "learning_rate": 0.00014444444444444444, "loss": 1.1541, "step": 110}, {"epoch": 1.2121212121212122, "grad_norm": 2.418940782546997, "learning_rate": 0.0001393939393939394, "loss": 1.0776, "step": 120}, {"epoch": 1.3131313131313131, "grad_norm": 2.603919744491577, "learning_rate": 0.00013434343434343436, "loss": 1.0823, "step": 130}, {"epoch": 1.4141414141414141, "grad_norm": 2.840055465698242, "learning_rate": 0.00012929292929292932, "loss": 1.0548, "step": 140}, {"epoch": 1.5151515151515151, "grad_norm": 2.690005302429199, "learning_rate": 0.00012424242424242425, "loss": 1.0021, "step": 150}, {"epoch": 1.6161616161616161, "grad_norm": 2.648704767227173, "learning_rate": 0.00011919191919191919, "loss": 0.954, "step": 160}, {"epoch": 1.7171717171717171, "grad_norm": 3.0394766330718994, "learning_rate": 0.00011414141414141415, "loss": 0.9306, "step": 170}, {"epoch": 1.8181818181818183, "grad_norm": 3.336500406265259, "learning_rate": 0.00010909090909090909, "loss": 0.8987, "step": 180}, {"epoch": 1.9191919191919191, "grad_norm": 2.857001543045044, "learning_rate": 0.00010404040404040405, "loss": 0.8868, "step": 190}, {"epoch": 2.0202020202020203, "grad_norm": 2.885561227798462, "learning_rate": 9.8989898989899e-05, "loss": 0.8222, "step": 200}], "logging_steps": 10, "max_steps": 396, "num_input_tokens_seen": 0, "num_train_epochs": 4, "save_steps": 100, "stateful_callbacks": {"TrainerControl": {"args": {"should_epoch_stop": false, "should_evaluate": false, "should_log": false, "should_save": true, "should_training_stop": false}, "attributes": {}}}, "total_flos": 1.386868439126016e+17, "train_batch_size": 16, "trial_name": null, "trial_params": null}