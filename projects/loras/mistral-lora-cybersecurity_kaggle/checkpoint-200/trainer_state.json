{"best_metric": null, "best_model_checkpoint": null, "epoch": 0.7104795737122558, "eval_steps": 500, "global_step": 200, "is_hyper_param_search": false, "is_local_process_zero": true, "is_world_process_zero": true, "log_history": [{"epoch": 0.003552397868561279, "grad_norm": 1.2869350910186768, "learning_rate": 0.0001998220640569395, "loss": 2.1993, "step": 1}, {"epoch": 0.035523978685612786, "grad_norm": 1.0716488361358643, "learning_rate": 0.000198220640569395, "loss": 2.1067, "step": 10}, {"epoch": 0.07104795737122557, "grad_norm": 0.998371422290802, "learning_rate": 0.00019644128113879004, "loss": 2.0574, "step": 20}, {"epoch": 0.10657193605683836, "grad_norm": 1.0013916492462158, "learning_rate": 0.00019466192170818506, "loss": 2.0637, "step": 30}, {"epoch": 0.14209591474245115, "grad_norm": 0.9159111380577087, "learning_rate": 0.0001928825622775801, "loss": 2.0604, "step": 40}, {"epoch": 0.17761989342806395, "grad_norm": 0.8729225993156433, "learning_rate": 0.0001911032028469751, "loss": 2.0787, "step": 50}, {"epoch": 0.21314387211367672, "grad_norm": 0.9349983334541321, "learning_rate": 0.0001893238434163701, "loss": 2.0422, "step": 60}, {"epoch": 0.24866785079928952, "grad_norm": 0.9006775617599487, "learning_rate": 0.00018754448398576514, "loss": 2.0508, "step": 70}, {"epoch": 0.2841918294849023, "grad_norm": 0.8842715620994568, "learning_rate": 0.00018576512455516017, "loss": 2.0633, "step": 80}, {"epoch": 0.3197158081705151, "grad_norm": 0.9161887764930725, "learning_rate": 0.00018398576512455517, "loss": 2.0305, "step": 90}, {"epoch": 0.3552397868561279, "grad_norm": 0.9918195009231567, "learning_rate": 0.00018220640569395016, "loss": 2.0282, "step": 100}, {"epoch": 0.3907637655417407, "grad_norm": 1.0437190532684326, "learning_rate": 0.00018042704626334522, "loss": 2.0469, "step": 110}, {"epoch": 0.42628774422735344, "grad_norm": 0.8895761370658875, "learning_rate": 0.00017864768683274022, "loss": 2.025, "step": 120}, {"epoch": 0.46181172291296624, "grad_norm": 0.9642046093940735, "learning_rate": 0.00017686832740213524, "loss": 2.0713, "step": 130}, {"epoch": 0.49733570159857904, "grad_norm": 1.0307921171188354, "learning_rate": 0.00017508896797153024, "loss": 2.0405, "step": 140}, {"epoch": 0.5328596802841918, "grad_norm": 0.9321216344833374, "learning_rate": 0.0001733096085409253, "loss": 2.0521, "step": 150}, {"epoch": 0.5683836589698046, "grad_norm": 1.0338728427886963, "learning_rate": 0.0001715302491103203, "loss": 2.0255, "step": 160}, {"epoch": 0.6039076376554174, "grad_norm": 1.0955281257629395, "learning_rate": 0.0001697508896797153, "loss": 1.99, "step": 170}, {"epoch": 0.6394316163410302, "grad_norm": 0.8815847039222717, "learning_rate": 0.00016797153024911032, "loss": 2.0421, "step": 180}, {"epoch": 0.6749555950266429, "grad_norm": 0.9438459277153015, "learning_rate": 0.00016619217081850535, "loss": 1.9666, "step": 190}, {"epoch": 0.7104795737122558, "grad_norm": 0.9337356090545654, "learning_rate": 0.00016441281138790037, "loss": 2.0649, "step": 200}], "logging_steps": 10, "max_steps": 1124, "num_input_tokens_seen": 0, "num_train_epochs": 4, "save_steps": 100, "stateful_callbacks": {"TrainerControl": {"args": {"should_epoch_stop": false, "should_evaluate": false, "should_log": false, "should_save": true, "should_training_stop": false}, "attributes": {}}}, "total_flos": 1.408794305200128e+17, "train_batch_size": 16, "trial_name": null, "trial_params": null}