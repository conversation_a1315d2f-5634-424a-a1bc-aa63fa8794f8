{"best_metric": null, "best_model_checkpoint": null, "epoch": 2.4866785079928952, "eval_steps": 500, "global_step": 700, "is_hyper_param_search": false, "is_local_process_zero": true, "is_world_process_zero": true, "log_history": [{"epoch": 0.003552397868561279, "grad_norm": 1.2869350910186768, "learning_rate": 0.0001998220640569395, "loss": 2.1993, "step": 1}, {"epoch": 0.035523978685612786, "grad_norm": 1.0716488361358643, "learning_rate": 0.000198220640569395, "loss": 2.1067, "step": 10}, {"epoch": 0.07104795737122557, "grad_norm": 0.998371422290802, "learning_rate": 0.00019644128113879004, "loss": 2.0574, "step": 20}, {"epoch": 0.10657193605683836, "grad_norm": 1.0013916492462158, "learning_rate": 0.00019466192170818506, "loss": 2.0637, "step": 30}, {"epoch": 0.14209591474245115, "grad_norm": 0.9159111380577087, "learning_rate": 0.0001928825622775801, "loss": 2.0604, "step": 40}, {"epoch": 0.17761989342806395, "grad_norm": 0.8729225993156433, "learning_rate": 0.0001911032028469751, "loss": 2.0787, "step": 50}, {"epoch": 0.21314387211367672, "grad_norm": 0.9349983334541321, "learning_rate": 0.0001893238434163701, "loss": 2.0422, "step": 60}, {"epoch": 0.24866785079928952, "grad_norm": 0.9006775617599487, "learning_rate": 0.00018754448398576514, "loss": 2.0508, "step": 70}, {"epoch": 0.2841918294849023, "grad_norm": 0.8842715620994568, "learning_rate": 0.00018576512455516017, "loss": 2.0633, "step": 80}, {"epoch": 0.3197158081705151, "grad_norm": 0.9161887764930725, "learning_rate": 0.00018398576512455517, "loss": 2.0305, "step": 90}, {"epoch": 0.3552397868561279, "grad_norm": 0.9918195009231567, "learning_rate": 0.00018220640569395016, "loss": 2.0282, "step": 100}, {"epoch": 0.3907637655417407, "grad_norm": 1.0437190532684326, "learning_rate": 0.00018042704626334522, "loss": 2.0469, "step": 110}, {"epoch": 0.42628774422735344, "grad_norm": 0.8895761370658875, "learning_rate": 0.00017864768683274022, "loss": 2.025, "step": 120}, {"epoch": 0.46181172291296624, "grad_norm": 0.9642046093940735, "learning_rate": 0.00017686832740213524, "loss": 2.0713, "step": 130}, {"epoch": 0.49733570159857904, "grad_norm": 1.0307921171188354, "learning_rate": 0.00017508896797153024, "loss": 2.0405, "step": 140}, {"epoch": 0.5328596802841918, "grad_norm": 0.9321216344833374, "learning_rate": 0.0001733096085409253, "loss": 2.0521, "step": 150}, {"epoch": 0.5683836589698046, "grad_norm": 1.0338728427886963, "learning_rate": 0.0001715302491103203, "loss": 2.0255, "step": 160}, {"epoch": 0.6039076376554174, "grad_norm": 1.0955281257629395, "learning_rate": 0.0001697508896797153, "loss": 1.99, "step": 170}, {"epoch": 0.6394316163410302, "grad_norm": 0.8815847039222717, "learning_rate": 0.00016797153024911032, "loss": 2.0421, "step": 180}, {"epoch": 0.6749555950266429, "grad_norm": 0.9438459277153015, "learning_rate": 0.00016619217081850535, "loss": 1.9666, "step": 190}, {"epoch": 0.7104795737122558, "grad_norm": 0.9337356090545654, "learning_rate": 0.00016441281138790037, "loss": 2.0649, "step": 200}, {"epoch": 0.7460035523978685, "grad_norm": 0.9555095434188843, "learning_rate": 0.00016263345195729537, "loss": 2.0465, "step": 210}, {"epoch": 0.7815275310834814, "grad_norm": 0.955778956413269, "learning_rate": 0.0001608540925266904, "loss": 2.0708, "step": 220}, {"epoch": 0.8170515097690941, "grad_norm": 1.017656683921814, "learning_rate": 0.00015907473309608543, "loss": 2.0389, "step": 230}, {"epoch": 0.8525754884547069, "grad_norm": 0.9434223175048828, "learning_rate": 0.00015729537366548045, "loss": 2.0666, "step": 240}, {"epoch": 0.8880994671403197, "grad_norm": 0.9457293748855591, "learning_rate": 0.00015551601423487545, "loss": 2.0623, "step": 250}, {"epoch": 0.9236234458259325, "grad_norm": 1.018613576889038, "learning_rate": 0.00015373665480427045, "loss": 2.0674, "step": 260}, {"epoch": 0.9591474245115453, "grad_norm": 0.9185274243354797, "learning_rate": 0.0001519572953736655, "loss": 2.102, "step": 270}, {"epoch": 0.9946714031971581, "grad_norm": 0.9822706580162048, "learning_rate": 0.0001501779359430605, "loss": 2.0604, "step": 280}, {"epoch": 1.030195381882771, "grad_norm": 1.053523302078247, "learning_rate": 0.00014839857651245553, "loss": 1.8695, "step": 290}, {"epoch": 1.0657193605683837, "grad_norm": 1.2371315956115723, "learning_rate": 0.00014661921708185053, "loss": 1.8018, "step": 300}, {"epoch": 1.1012433392539964, "grad_norm": 1.2057156562805176, "learning_rate": 0.00014483985765124558, "loss": 1.8028, "step": 310}, {"epoch": 1.1367673179396092, "grad_norm": 1.3080638647079468, "learning_rate": 0.00014306049822064058, "loss": 1.7777, "step": 320}, {"epoch": 1.1722912966252221, "grad_norm": 1.4501802921295166, "learning_rate": 0.0001412811387900356, "loss": 1.7618, "step": 330}, {"epoch": 1.2078152753108349, "grad_norm": 1.3310002088546753, "learning_rate": 0.0001395017793594306, "loss": 1.7771, "step": 340}, {"epoch": 1.2433392539964476, "grad_norm": 1.323794960975647, "learning_rate": 0.0001377224199288256, "loss": 1.771, "step": 350}, {"epoch": 1.2788632326820604, "grad_norm": 1.4723939895629883, "learning_rate": 0.00013594306049822066, "loss": 1.7545, "step": 360}, {"epoch": 1.3143872113676731, "grad_norm": 1.3816437721252441, "learning_rate": 0.00013416370106761566, "loss": 1.7305, "step": 370}, {"epoch": 1.349911190053286, "grad_norm": 1.3322738409042358, "learning_rate": 0.00013238434163701069, "loss": 1.7728, "step": 380}, {"epoch": 1.3854351687388988, "grad_norm": 1.5022059679031372, "learning_rate": 0.00013060498220640568, "loss": 1.7698, "step": 390}, {"epoch": 1.4209591474245116, "grad_norm": 1.342334270477295, "learning_rate": 0.00012882562277580074, "loss": 1.7583, "step": 400}, {"epoch": 1.4564831261101243, "grad_norm": 1.3931387662887573, "learning_rate": 0.00012704626334519574, "loss": 1.7823, "step": 410}, {"epoch": 1.492007104795737, "grad_norm": 1.4308767318725586, "learning_rate": 0.00012526690391459074, "loss": 1.7502, "step": 420}, {"epoch": 1.52753108348135, "grad_norm": 1.4961824417114258, "learning_rate": 0.00012348754448398576, "loss": 1.7939, "step": 430}, {"epoch": 1.5630550621669625, "grad_norm": 1.5661557912826538, "learning_rate": 0.0001217081850533808, "loss": 1.7692, "step": 440}, {"epoch": 1.5985790408525755, "grad_norm": 1.429015040397644, "learning_rate": 0.00011992882562277582, "loss": 1.7657, "step": 450}, {"epoch": 1.6341030195381883, "grad_norm": 1.517441749572754, "learning_rate": 0.00011814946619217081, "loss": 1.7496, "step": 460}, {"epoch": 1.669626998223801, "grad_norm": 1.5529022216796875, "learning_rate": 0.00011637010676156583, "loss": 1.7319, "step": 470}, {"epoch": 1.705150976909414, "grad_norm": 1.4822005033493042, "learning_rate": 0.00011459074733096087, "loss": 1.7406, "step": 480}, {"epoch": 1.7406749555950265, "grad_norm": 1.4920531511306763, "learning_rate": 0.00011281138790035588, "loss": 1.8051, "step": 490}, {"epoch": 1.7761989342806395, "grad_norm": 1.5531044006347656, "learning_rate": 0.00011103202846975089, "loss": 1.7594, "step": 500}, {"epoch": 1.8117229129662522, "grad_norm": 1.5629295110702515, "learning_rate": 0.0001092526690391459, "loss": 1.7719, "step": 510}, {"epoch": 1.847246891651865, "grad_norm": 1.5780284404754639, "learning_rate": 0.00010747330960854095, "loss": 1.7654, "step": 520}, {"epoch": 1.882770870337478, "grad_norm": 2.5400888919830322, "learning_rate": 0.00010569395017793596, "loss": 1.7603, "step": 530}, {"epoch": 1.9182948490230904, "grad_norm": 1.5801606178283691, "learning_rate": 0.00010391459074733096, "loss": 1.7335, "step": 540}, {"epoch": 1.9538188277087034, "grad_norm": 1.6594690084457397, "learning_rate": 0.00010213523131672597, "loss": 1.8189, "step": 550}, {"epoch": 1.9893428063943162, "grad_norm": 1.5161433219909668, "learning_rate": 0.00010035587188612101, "loss": 1.8019, "step": 560}, {"epoch": 2.024866785079929, "grad_norm": 7.213324069976807, "learning_rate": 9.857651245551602e-05, "loss": 1.5581, "step": 570}, {"epoch": 2.060390763765542, "grad_norm": 2.5190882682800293, "learning_rate": 9.679715302491104e-05, "loss": 1.4078, "step": 580}, {"epoch": 2.0959147424511544, "grad_norm": 2.042520046234131, "learning_rate": 9.501779359430606e-05, "loss": 1.4021, "step": 590}, {"epoch": 2.1314387211367674, "grad_norm": 2.0174052715301514, "learning_rate": 9.323843416370108e-05, "loss": 1.3961, "step": 600}, {"epoch": 2.1669626998223803, "grad_norm": 2.0110650062561035, "learning_rate": 9.14590747330961e-05, "loss": 1.3815, "step": 610}, {"epoch": 2.202486678507993, "grad_norm": 2.0568461418151855, "learning_rate": 8.96797153024911e-05, "loss": 1.4226, "step": 620}, {"epoch": 2.238010657193606, "grad_norm": 2.22839617729187, "learning_rate": 8.790035587188611e-05, "loss": 1.3717, "step": 630}, {"epoch": 2.2735346358792183, "grad_norm": 2.1079893112182617, "learning_rate": 8.612099644128114e-05, "loss": 1.4019, "step": 640}, {"epoch": 2.3090586145648313, "grad_norm": 2.2104227542877197, "learning_rate": 8.434163701067615e-05, "loss": 1.4031, "step": 650}, {"epoch": 2.3445825932504443, "grad_norm": 2.1849746704101562, "learning_rate": 8.256227758007118e-05, "loss": 1.3979, "step": 660}, {"epoch": 2.380106571936057, "grad_norm": 2.289170742034912, "learning_rate": 8.078291814946619e-05, "loss": 1.3872, "step": 670}, {"epoch": 2.4156305506216698, "grad_norm": 2.3695757389068604, "learning_rate": 7.900355871886122e-05, "loss": 1.397, "step": 680}, {"epoch": 2.4511545293072823, "grad_norm": 2.2477712631225586, "learning_rate": 7.722419928825623e-05, "loss": 1.4111, "step": 690}, {"epoch": 2.4866785079928952, "grad_norm": 2.2196977138519287, "learning_rate": 7.544483985765126e-05, "loss": 1.4151, "step": 700}], "logging_steps": 10, "max_steps": 1124, "num_input_tokens_seen": 0, "num_train_epochs": 4, "save_steps": 100, "stateful_callbacks": {"TrainerControl": {"args": {"should_epoch_stop": false, "should_evaluate": false, "should_log": false, "should_save": true, "should_training_stop": false}, "attributes": {}}}, "total_flos": 4.928134480625664e+17, "train_batch_size": 16, "trial_name": null, "trial_params": null}