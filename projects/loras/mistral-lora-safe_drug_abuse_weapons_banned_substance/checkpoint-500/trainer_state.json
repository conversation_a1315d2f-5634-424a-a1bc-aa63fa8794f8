{"best_metric": null, "best_model_checkpoint": null, "epoch": 2.2988505747126435, "eval_steps": 500, "global_step": 500, "is_hyper_param_search": false, "is_local_process_zero": true, "is_world_process_zero": true, "log_history": [{"epoch": 0.004597701149425287, "grad_norm": 2.970977783203125, "learning_rate": 0.00019976958525345624, "loss": 1.3891, "step": 1}, {"epoch": 0.04597701149425287, "grad_norm": 1.4740029573440552, "learning_rate": 0.00019769585253456222, "loss": 1.1313, "step": 10}, {"epoch": 0.09195402298850575, "grad_norm": 1.1520805358886719, "learning_rate": 0.00019539170506912442, "loss": 1.0176, "step": 20}, {"epoch": 0.13793103448275862, "grad_norm": 1.3123767375946045, "learning_rate": 0.00019308755760368663, "loss": 0.9845, "step": 30}, {"epoch": 0.1839080459770115, "grad_norm": 1.2529566287994385, "learning_rate": 0.00019078341013824886, "loss": 0.9525, "step": 40}, {"epoch": 0.22988505747126436, "grad_norm": 1.1751121282577515, "learning_rate": 0.00018847926267281107, "loss": 0.9544, "step": 50}, {"epoch": 0.27586206896551724, "grad_norm": 1.292291522026062, "learning_rate": 0.00018617511520737328, "loss": 0.9268, "step": 60}, {"epoch": 0.3218390804597701, "grad_norm": 1.2888182401657104, "learning_rate": 0.00018387096774193548, "loss": 0.904, "step": 70}, {"epoch": 0.367816091954023, "grad_norm": 1.283928394317627, "learning_rate": 0.0001815668202764977, "loss": 0.8921, "step": 80}, {"epoch": 0.41379310344827586, "grad_norm": 1.2274566888809204, "learning_rate": 0.0001792626728110599, "loss": 0.8875, "step": 90}, {"epoch": 0.45977011494252873, "grad_norm": 1.2334316968917847, "learning_rate": 0.00017695852534562213, "loss": 0.8744, "step": 100}, {"epoch": 0.5057471264367817, "grad_norm": 1.2717163562774658, "learning_rate": 0.00017465437788018436, "loss": 0.8775, "step": 110}, {"epoch": 0.5517241379310345, "grad_norm": 1.2945350408554077, "learning_rate": 0.00017235023041474657, "loss": 0.8674, "step": 120}, {"epoch": 0.5977011494252874, "grad_norm": 1.4390883445739746, "learning_rate": 0.00017004608294930878, "loss": 0.8522, "step": 130}, {"epoch": 0.6436781609195402, "grad_norm": 1.2073755264282227, "learning_rate": 0.00016774193548387098, "loss": 0.8425, "step": 140}, {"epoch": 0.6896551724137931, "grad_norm": 1.202459454536438, "learning_rate": 0.0001654377880184332, "loss": 0.8399, "step": 150}, {"epoch": 0.735632183908046, "grad_norm": 1.1934398412704468, "learning_rate": 0.0001631336405529954, "loss": 0.8578, "step": 160}, {"epoch": 0.7816091954022989, "grad_norm": 1.1823420524597168, "learning_rate": 0.0001608294930875576, "loss": 0.8534, "step": 170}, {"epoch": 0.8275862068965517, "grad_norm": 1.3156769275665283, "learning_rate": 0.00015852534562211984, "loss": 0.8454, "step": 180}, {"epoch": 0.8735632183908046, "grad_norm": 1.1394882202148438, "learning_rate": 0.00015622119815668204, "loss": 0.815, "step": 190}, {"epoch": 0.9195402298850575, "grad_norm": 1.1277618408203125, "learning_rate": 0.00015391705069124425, "loss": 0.8405, "step": 200}, {"epoch": 0.9655172413793104, "grad_norm": 1.3437188863754272, "learning_rate": 0.00015161290322580646, "loss": 0.8413, "step": 210}, {"epoch": 1.0114942528735633, "grad_norm": 1.1857802867889404, "learning_rate": 0.00014930875576036866, "loss": 0.7797, "step": 220}, {"epoch": 1.0574712643678161, "grad_norm": 1.2988855838775635, "learning_rate": 0.00014700460829493087, "loss": 0.6595, "step": 230}, {"epoch": 1.103448275862069, "grad_norm": 1.268661618232727, "learning_rate": 0.0001447004608294931, "loss": 0.6508, "step": 240}, {"epoch": 1.1494252873563218, "grad_norm": 1.3557019233703613, "learning_rate": 0.0001423963133640553, "loss": 0.6391, "step": 250}, {"epoch": 1.1954022988505748, "grad_norm": 1.2805923223495483, "learning_rate": 0.00014009216589861752, "loss": 0.6552, "step": 260}, {"epoch": 1.2413793103448276, "grad_norm": 1.279948115348816, "learning_rate": 0.00013778801843317972, "loss": 0.6507, "step": 270}, {"epoch": 1.2873563218390804, "grad_norm": 1.2816520929336548, "learning_rate": 0.00013548387096774193, "loss": 0.6363, "step": 280}, {"epoch": 1.3333333333333333, "grad_norm": 1.2335567474365234, "learning_rate": 0.00013317972350230414, "loss": 0.6469, "step": 290}, {"epoch": 1.3793103448275863, "grad_norm": 1.289137601852417, "learning_rate": 0.00013087557603686637, "loss": 0.6444, "step": 300}, {"epoch": 1.4252873563218391, "grad_norm": 1.3048689365386963, "learning_rate": 0.00012857142857142858, "loss": 0.6603, "step": 310}, {"epoch": 1.471264367816092, "grad_norm": 1.4020261764526367, "learning_rate": 0.0001262672811059908, "loss": 0.6762, "step": 320}, {"epoch": 1.5172413793103448, "grad_norm": 1.513372540473938, "learning_rate": 0.00012396313364055302, "loss": 0.6569, "step": 330}, {"epoch": 1.5632183908045976, "grad_norm": 1.2211661338806152, "learning_rate": 0.00012165898617511522, "loss": 0.6475, "step": 340}, {"epoch": 1.6091954022988506, "grad_norm": 1.2229307889938354, "learning_rate": 0.00011935483870967743, "loss": 0.6528, "step": 350}, {"epoch": 1.6551724137931034, "grad_norm": 1.479101300239563, "learning_rate": 0.00011705069124423964, "loss": 0.6692, "step": 360}, {"epoch": 1.7011494252873565, "grad_norm": 1.3153586387634277, "learning_rate": 0.00011474654377880186, "loss": 0.6534, "step": 370}, {"epoch": 1.7471264367816093, "grad_norm": 1.2446802854537964, "learning_rate": 0.00011244239631336406, "loss": 0.6511, "step": 380}, {"epoch": 1.793103448275862, "grad_norm": 1.2973923683166504, "learning_rate": 0.00011013824884792627, "loss": 0.6761, "step": 390}, {"epoch": 1.839080459770115, "grad_norm": 1.4919346570968628, "learning_rate": 0.00010783410138248849, "loss": 0.6457, "step": 400}, {"epoch": 1.8850574712643677, "grad_norm": 1.2974845170974731, "learning_rate": 0.0001055299539170507, "loss": 0.6529, "step": 410}, {"epoch": 1.9310344827586206, "grad_norm": 1.2758647203445435, "learning_rate": 0.0001032258064516129, "loss": 0.6427, "step": 420}, {"epoch": 1.9770114942528736, "grad_norm": 1.2757261991500854, "learning_rate": 0.00010092165898617512, "loss": 0.6308, "step": 430}, {"epoch": 2.0229885057471266, "grad_norm": 1.4119415283203125, "learning_rate": 9.861751152073733e-05, "loss": 0.5752, "step": 440}, {"epoch": 2.0689655172413794, "grad_norm": 1.5322794914245605, "learning_rate": 9.631336405529955e-05, "loss": 0.472, "step": 450}, {"epoch": 2.1149425287356323, "grad_norm": 1.5412267446517944, "learning_rate": 9.400921658986176e-05, "loss": 0.4741, "step": 460}, {"epoch": 2.160919540229885, "grad_norm": 1.6093143224716187, "learning_rate": 9.170506912442398e-05, "loss": 0.4707, "step": 470}, {"epoch": 2.206896551724138, "grad_norm": 1.701107382774353, "learning_rate": 8.940092165898618e-05, "loss": 0.46, "step": 480}, {"epoch": 2.2528735632183907, "grad_norm": 1.57436203956604, "learning_rate": 8.709677419354839e-05, "loss": 0.4731, "step": 490}, {"epoch": 2.2988505747126435, "grad_norm": 1.4849790334701538, "learning_rate": 8.479262672811061e-05, "loss": 0.4591, "step": 500}], "logging_steps": 10, "max_steps": 868, "num_input_tokens_seen": 0, "num_train_epochs": 4, "save_steps": 100, "stateful_callbacks": {"TrainerControl": {"args": {"should_epoch_stop": false, "should_evaluate": false, "should_log": false, "should_save": true, "should_training_stop": false}, "attributes": {}}}, "total_flos": 3.508406914940928e+17, "train_batch_size": 16, "trial_name": null, "trial_params": null}