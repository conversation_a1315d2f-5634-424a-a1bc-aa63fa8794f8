{"best_metric": null, "best_model_checkpoint": null, "epoch": 0.9195402298850575, "eval_steps": 500, "global_step": 200, "is_hyper_param_search": false, "is_local_process_zero": true, "is_world_process_zero": true, "log_history": [{"epoch": 0.004597701149425287, "grad_norm": 2.970977783203125, "learning_rate": 0.00019976958525345624, "loss": 1.3891, "step": 1}, {"epoch": 0.04597701149425287, "grad_norm": 1.4740029573440552, "learning_rate": 0.00019769585253456222, "loss": 1.1313, "step": 10}, {"epoch": 0.09195402298850575, "grad_norm": 1.1520805358886719, "learning_rate": 0.00019539170506912442, "loss": 1.0176, "step": 20}, {"epoch": 0.13793103448275862, "grad_norm": 1.3123767375946045, "learning_rate": 0.00019308755760368663, "loss": 0.9845, "step": 30}, {"epoch": 0.1839080459770115, "grad_norm": 1.2529566287994385, "learning_rate": 0.00019078341013824886, "loss": 0.9525, "step": 40}, {"epoch": 0.22988505747126436, "grad_norm": 1.1751121282577515, "learning_rate": 0.00018847926267281107, "loss": 0.9544, "step": 50}, {"epoch": 0.27586206896551724, "grad_norm": 1.292291522026062, "learning_rate": 0.00018617511520737328, "loss": 0.9268, "step": 60}, {"epoch": 0.3218390804597701, "grad_norm": 1.2888182401657104, "learning_rate": 0.00018387096774193548, "loss": 0.904, "step": 70}, {"epoch": 0.367816091954023, "grad_norm": 1.283928394317627, "learning_rate": 0.0001815668202764977, "loss": 0.8921, "step": 80}, {"epoch": 0.41379310344827586, "grad_norm": 1.2274566888809204, "learning_rate": 0.0001792626728110599, "loss": 0.8875, "step": 90}, {"epoch": 0.45977011494252873, "grad_norm": 1.2334316968917847, "learning_rate": 0.00017695852534562213, "loss": 0.8744, "step": 100}, {"epoch": 0.5057471264367817, "grad_norm": 1.2717163562774658, "learning_rate": 0.00017465437788018436, "loss": 0.8775, "step": 110}, {"epoch": 0.5517241379310345, "grad_norm": 1.2945350408554077, "learning_rate": 0.00017235023041474657, "loss": 0.8674, "step": 120}, {"epoch": 0.5977011494252874, "grad_norm": 1.4390883445739746, "learning_rate": 0.00017004608294930878, "loss": 0.8522, "step": 130}, {"epoch": 0.6436781609195402, "grad_norm": 1.2073755264282227, "learning_rate": 0.00016774193548387098, "loss": 0.8425, "step": 140}, {"epoch": 0.6896551724137931, "grad_norm": 1.202459454536438, "learning_rate": 0.0001654377880184332, "loss": 0.8399, "step": 150}, {"epoch": 0.735632183908046, "grad_norm": 1.1934398412704468, "learning_rate": 0.0001631336405529954, "loss": 0.8578, "step": 160}, {"epoch": 0.7816091954022989, "grad_norm": 1.1823420524597168, "learning_rate": 0.0001608294930875576, "loss": 0.8534, "step": 170}, {"epoch": 0.8275862068965517, "grad_norm": 1.3156769275665283, "learning_rate": 0.00015852534562211984, "loss": 0.8454, "step": 180}, {"epoch": 0.8735632183908046, "grad_norm": 1.1394882202148438, "learning_rate": 0.00015622119815668204, "loss": 0.815, "step": 190}, {"epoch": 0.9195402298850575, "grad_norm": 1.1277618408203125, "learning_rate": 0.00015391705069124425, "loss": 0.8405, "step": 200}], "logging_steps": 10, "max_steps": 868, "num_input_tokens_seen": 0, "num_train_epochs": 4, "save_steps": 100, "stateful_callbacks": {"TrainerControl": {"args": {"should_epoch_stop": false, "should_evaluate": false, "should_log": false, "should_save": true, "should_training_stop": false}, "attributes": {}}}, "total_flos": 1.405044382629888e+17, "train_batch_size": 16, "trial_name": null, "trial_params": null}