{"best_metric": null, "best_model_checkpoint": null, "epoch": 3.9928952042628776, "eval_steps": 500, "global_step": 1124, "is_hyper_param_search": false, "is_local_process_zero": true, "is_world_process_zero": true, "log_history": [{"epoch": 0.003552397868561279, "grad_norm": 3.9962735176086426, "learning_rate": 0.0001998220640569395, "loss": 2.4142, "step": 1}, {"epoch": 0.035523978685612786, "grad_norm": 1.4869052171707153, "learning_rate": 0.000198220640569395, "loss": 2.1903, "step": 10}, {"epoch": 0.07104795737122557, "grad_norm": 1.1331593990325928, "learning_rate": 0.00019644128113879004, "loss": 2.088, "step": 20}, {"epoch": 0.10657193605683836, "grad_norm": 1.1903799772262573, "learning_rate": 0.00019466192170818506, "loss": 2.1407, "step": 30}, {"epoch": 0.14209591474245115, "grad_norm": 1.1330046653747559, "learning_rate": 0.0001928825622775801, "loss": 2.1442, "step": 40}, {"epoch": 0.17761989342806395, "grad_norm": 1.125049352645874, "learning_rate": 0.0001911032028469751, "loss": 2.1097, "step": 50}, {"epoch": 0.21314387211367672, "grad_norm": 1.2214674949645996, "learning_rate": 0.0001893238434163701, "loss": 2.1004, "step": 60}, {"epoch": 0.24866785079928952, "grad_norm": 1.2321404218673706, "learning_rate": 0.00018754448398576514, "loss": 2.0954, "step": 70}, {"epoch": 0.2841918294849023, "grad_norm": 1.419464111328125, "learning_rate": 0.00018576512455516017, "loss": 2.0951, "step": 80}, {"epoch": 0.3197158081705151, "grad_norm": 1.28981351852417, "learning_rate": 0.00018398576512455517, "loss": 2.133, "step": 90}, {"epoch": 0.3552397868561279, "grad_norm": 1.2521274089813232, "learning_rate": 0.00018220640569395016, "loss": 2.0934, "step": 100}, {"epoch": 0.3907637655417407, "grad_norm": 1.336744785308838, "learning_rate": 0.00018042704626334522, "loss": 2.1169, "step": 110}, {"epoch": 0.42628774422735344, "grad_norm": 1.0756571292877197, "learning_rate": 0.00017864768683274022, "loss": 2.0847, "step": 120}, {"epoch": 0.46181172291296624, "grad_norm": 1.437597393989563, "learning_rate": 0.00017686832740213524, "loss": 2.0846, "step": 130}, {"epoch": 0.49733570159857904, "grad_norm": 1.1811530590057373, "learning_rate": 0.00017508896797153024, "loss": 2.1063, "step": 140}, {"epoch": 0.5328596802841918, "grad_norm": 1.3379446268081665, "learning_rate": 0.0001733096085409253, "loss": 2.0957, "step": 150}, {"epoch": 0.5683836589698046, "grad_norm": 1.4486815929412842, "learning_rate": 0.0001715302491103203, "loss": 2.0939, "step": 160}, {"epoch": 0.6039076376554174, "grad_norm": 1.3667750358581543, "learning_rate": 0.0001697508896797153, "loss": 2.1111, "step": 170}, {"epoch": 0.6394316163410302, "grad_norm": 1.4012879133224487, "learning_rate": 0.00016797153024911032, "loss": 2.1121, "step": 180}, {"epoch": 0.6749555950266429, "grad_norm": 1.454379916191101, "learning_rate": 0.00016619217081850535, "loss": 2.0939, "step": 190}, {"epoch": 0.7104795737122558, "grad_norm": 1.3121262788772583, "learning_rate": 0.00016441281138790037, "loss": 2.0747, "step": 200}, {"epoch": 0.7460035523978685, "grad_norm": 1.4184110164642334, "learning_rate": 0.00016263345195729537, "loss": 2.0942, "step": 210}, {"epoch": 0.7815275310834814, "grad_norm": 1.491707444190979, "learning_rate": 0.0001608540925266904, "loss": 2.103, "step": 220}, {"epoch": 0.8170515097690941, "grad_norm": 1.3315728902816772, "learning_rate": 0.00015907473309608543, "loss": 2.0579, "step": 230}, {"epoch": 0.8525754884547069, "grad_norm": 1.2967815399169922, "learning_rate": 0.00015729537366548045, "loss": 2.0715, "step": 240}, {"epoch": 0.8880994671403197, "grad_norm": 1.453351378440857, "learning_rate": 0.00015551601423487545, "loss": 2.0534, "step": 250}, {"epoch": 0.9236234458259325, "grad_norm": 1.4781614542007446, "learning_rate": 0.00015373665480427045, "loss": 2.0703, "step": 260}, {"epoch": 0.9591474245115453, "grad_norm": 1.3337165117263794, "learning_rate": 0.0001519572953736655, "loss": 2.0806, "step": 270}, {"epoch": 0.9946714031971581, "grad_norm": 1.2729237079620361, "learning_rate": 0.0001501779359430605, "loss": 2.0932, "step": 280}, {"epoch": 1.030195381882771, "grad_norm": 2.4112932682037354, "learning_rate": 0.00014839857651245553, "loss": 1.7785, "step": 290}, {"epoch": 1.0657193605683837, "grad_norm": 1.66653311252594, "learning_rate": 0.00014661921708185053, "loss": 1.7195, "step": 300}, {"epoch": 1.1012433392539964, "grad_norm": 1.7988224029541016, "learning_rate": 0.00014483985765124558, "loss": 1.7039, "step": 310}, {"epoch": 1.1367673179396092, "grad_norm": 1.7742470502853394, "learning_rate": 0.00014306049822064058, "loss": 1.7016, "step": 320}, {"epoch": 1.1722912966252221, "grad_norm": 1.582621693611145, "learning_rate": 0.0001412811387900356, "loss": 1.6954, "step": 330}, {"epoch": 1.2078152753108349, "grad_norm": 1.8914742469787598, "learning_rate": 0.0001395017793594306, "loss": 1.6994, "step": 340}, {"epoch": 1.2433392539964476, "grad_norm": 1.8221203088760376, "learning_rate": 0.0001377224199288256, "loss": 1.7111, "step": 350}, {"epoch": 1.2788632326820604, "grad_norm": 2.0181615352630615, "learning_rate": 0.00013594306049822066, "loss": 1.7352, "step": 360}, {"epoch": 1.3143872113676731, "grad_norm": 1.814638376235962, "learning_rate": 0.00013416370106761566, "loss": 1.705, "step": 370}, {"epoch": 1.349911190053286, "grad_norm": 1.8362360000610352, "learning_rate": 0.00013238434163701069, "loss": 1.7217, "step": 380}, {"epoch": 1.3854351687388988, "grad_norm": 1.957939624786377, "learning_rate": 0.00013060498220640568, "loss": 1.7064, "step": 390}, {"epoch": 1.4209591474245116, "grad_norm": 2.0994350910186768, "learning_rate": 0.00012882562277580074, "loss": 1.7102, "step": 400}, {"epoch": 1.4564831261101243, "grad_norm": 1.931003212928772, "learning_rate": 0.00012704626334519574, "loss": 1.6888, "step": 410}, {"epoch": 1.492007104795737, "grad_norm": 1.990833044052124, "learning_rate": 0.00012526690391459074, "loss": 1.704, "step": 420}, {"epoch": 1.52753108348135, "grad_norm": 2.2460672855377197, "learning_rate": 0.00012348754448398576, "loss": 1.6953, "step": 430}, {"epoch": 1.5630550621669625, "grad_norm": 2.2004170417785645, "learning_rate": 0.0001217081850533808, "loss": 1.6934, "step": 440}, {"epoch": 1.5985790408525755, "grad_norm": 2.0524439811706543, "learning_rate": 0.00011992882562277582, "loss": 1.7118, "step": 450}, {"epoch": 1.6341030195381883, "grad_norm": 2.032142400741577, "learning_rate": 0.00011814946619217081, "loss": 1.6806, "step": 460}, {"epoch": 1.669626998223801, "grad_norm": 1.8902246952056885, "learning_rate": 0.00011637010676156583, "loss": 1.7102, "step": 470}, {"epoch": 1.705150976909414, "grad_norm": 2.0225980281829834, "learning_rate": 0.00011459074733096087, "loss": 1.6862, "step": 480}, {"epoch": 1.7406749555950265, "grad_norm": 1.95848548412323, "learning_rate": 0.00011281138790035588, "loss": 1.6676, "step": 490}, {"epoch": 1.7761989342806395, "grad_norm": 2.05471134185791, "learning_rate": 0.00011103202846975089, "loss": 1.6974, "step": 500}, {"epoch": 1.8117229129662522, "grad_norm": 1.9341111183166504, "learning_rate": 0.0001092526690391459, "loss": 1.674, "step": 510}, {"epoch": 1.847246891651865, "grad_norm": 1.9616025686264038, "learning_rate": 0.00010747330960854095, "loss": 1.673, "step": 520}, {"epoch": 1.882770870337478, "grad_norm": 2.2054648399353027, "learning_rate": 0.00010569395017793596, "loss": 1.6758, "step": 530}, {"epoch": 1.9182948490230904, "grad_norm": 2.125812530517578, "learning_rate": 0.00010391459074733096, "loss": 1.7233, "step": 540}, {"epoch": 1.9538188277087034, "grad_norm": 2.0131733417510986, "learning_rate": 0.00010213523131672597, "loss": 1.6906, "step": 550}, {"epoch": 1.9893428063943162, "grad_norm": 2.276534080505371, "learning_rate": 0.00010035587188612101, "loss": 1.709, "step": 560}, {"epoch": 2.024866785079929, "grad_norm": 3.773772954940796, "learning_rate": 9.857651245551602e-05, "loss": 1.3694, "step": 570}, {"epoch": 2.060390763765542, "grad_norm": 3.093200206756592, "learning_rate": 9.679715302491104e-05, "loss": 1.254, "step": 580}, {"epoch": 2.0959147424511544, "grad_norm": 3.067634105682373, "learning_rate": 9.501779359430606e-05, "loss": 1.2274, "step": 590}, {"epoch": 2.1314387211367674, "grad_norm": 3.1156020164489746, "learning_rate": 9.323843416370108e-05, "loss": 1.2046, "step": 600}, {"epoch": 2.1669626998223803, "grad_norm": 2.6578991413116455, "learning_rate": 9.14590747330961e-05, "loss": 1.266, "step": 610}, {"epoch": 2.202486678507993, "grad_norm": 3.306873321533203, "learning_rate": 8.96797153024911e-05, "loss": 1.2393, "step": 620}, {"epoch": 2.238010657193606, "grad_norm": 3.125260591506958, "learning_rate": 8.790035587188611e-05, "loss": 1.2485, "step": 630}, {"epoch": 2.2735346358792183, "grad_norm": 3.3105878829956055, "learning_rate": 8.612099644128114e-05, "loss": 1.2457, "step": 640}, {"epoch": 2.3090586145648313, "grad_norm": 2.878399133682251, "learning_rate": 8.434163701067615e-05, "loss": 1.2333, "step": 650}, {"epoch": 2.3445825932504443, "grad_norm": 3.5747604370117188, "learning_rate": 8.256227758007118e-05, "loss": 1.2548, "step": 660}, {"epoch": 2.380106571936057, "grad_norm": 3.1808271408081055, "learning_rate": 8.078291814946619e-05, "loss": 1.2301, "step": 670}, {"epoch": 2.4156305506216698, "grad_norm": 2.991732597351074, "learning_rate": 7.900355871886122e-05, "loss": 1.2482, "step": 680}, {"epoch": 2.4511545293072823, "grad_norm": 3.132713794708252, "learning_rate": 7.722419928825623e-05, "loss": 1.2325, "step": 690}, {"epoch": 2.4866785079928952, "grad_norm": 3.168057918548584, "learning_rate": 7.544483985765126e-05, "loss": 1.2487, "step": 700}, {"epoch": 2.522202486678508, "grad_norm": 3.710782766342163, "learning_rate": 7.366548042704626e-05, "loss": 1.2622, "step": 710}, {"epoch": 2.5577264653641207, "grad_norm": 3.323829174041748, "learning_rate": 7.188612099644128e-05, "loss": 1.2305, "step": 720}, {"epoch": 2.5932504440497337, "grad_norm": 3.1022238731384277, "learning_rate": 7.01067615658363e-05, "loss": 1.2573, "step": 730}, {"epoch": 2.6287744227353462, "grad_norm": 3.4193496704101562, "learning_rate": 6.832740213523132e-05, "loss": 1.2038, "step": 740}, {"epoch": 2.664298401420959, "grad_norm": 3.00113582611084, "learning_rate": 6.654804270462633e-05, "loss": 1.2573, "step": 750}, {"epoch": 2.699822380106572, "grad_norm": 2.864305019378662, "learning_rate": 6.476868327402136e-05, "loss": 1.2676, "step": 760}, {"epoch": 2.7353463587921847, "grad_norm": 2.9938571453094482, "learning_rate": 6.298932384341637e-05, "loss": 1.2612, "step": 770}, {"epoch": 2.7708703374777977, "grad_norm": 3.1785292625427246, "learning_rate": 6.12099644128114e-05, "loss": 1.2666, "step": 780}, {"epoch": 2.80639431616341, "grad_norm": 3.030517816543579, "learning_rate": 5.9430604982206406e-05, "loss": 1.2409, "step": 790}, {"epoch": 2.841918294849023, "grad_norm": 2.962907075881958, "learning_rate": 5.765124555160143e-05, "loss": 1.211, "step": 800}, {"epoch": 2.877442273534636, "grad_norm": 3.1182968616485596, "learning_rate": 5.587188612099644e-05, "loss": 1.2522, "step": 810}, {"epoch": 2.9129662522202486, "grad_norm": 3.4860455989837646, "learning_rate": 5.4092526690391465e-05, "loss": 1.2486, "step": 820}, {"epoch": 2.9484902309058616, "grad_norm": 3.264042615890503, "learning_rate": 5.231316725978648e-05, "loss": 1.2473, "step": 830}, {"epoch": 2.984014209591474, "grad_norm": 3.337961435317993, "learning_rate": 5.0533807829181504e-05, "loss": 1.24, "step": 840}, {"epoch": 3.019538188277087, "grad_norm": 4.316161155700684, "learning_rate": 4.875444839857651e-05, "loss": 1.0326, "step": 850}, {"epoch": 3.0550621669626996, "grad_norm": 4.054572105407715, "learning_rate": 4.697508896797153e-05, "loss": 0.8469, "step": 860}, {"epoch": 3.0905861456483126, "grad_norm": 4.742246150970459, "learning_rate": 4.519572953736655e-05, "loss": 0.8573, "step": 870}, {"epoch": 3.1261101243339255, "grad_norm": 4.136750221252441, "learning_rate": 4.341637010676157e-05, "loss": 0.8376, "step": 880}, {"epoch": 3.161634103019538, "grad_norm": 4.171172142028809, "learning_rate": 4.163701067615658e-05, "loss": 0.8538, "step": 890}, {"epoch": 3.197158081705151, "grad_norm": 3.98291015625, "learning_rate": 3.98576512455516e-05, "loss": 0.8507, "step": 900}, {"epoch": 3.232682060390764, "grad_norm": 4.237030982971191, "learning_rate": 3.807829181494662e-05, "loss": 0.8719, "step": 910}, {"epoch": 3.2682060390763765, "grad_norm": 4.349482536315918, "learning_rate": 3.629893238434164e-05, "loss": 0.855, "step": 920}, {"epoch": 3.3037300177619895, "grad_norm": 4.47939395904541, "learning_rate": 3.451957295373665e-05, "loss": 0.8637, "step": 930}, {"epoch": 3.339253996447602, "grad_norm": 4.329283237457275, "learning_rate": 3.274021352313167e-05, "loss": 0.8664, "step": 940}, {"epoch": 3.374777975133215, "grad_norm": 4.373620986938477, "learning_rate": 3.096085409252669e-05, "loss": 0.8615, "step": 950}, {"epoch": 3.410301953818828, "grad_norm": 4.3457183837890625, "learning_rate": 2.918149466192171e-05, "loss": 0.8507, "step": 960}, {"epoch": 3.4458259325044405, "grad_norm": 4.0937018394470215, "learning_rate": 2.7402135231316728e-05, "loss": 0.8454, "step": 970}, {"epoch": 3.4813499111900534, "grad_norm": 4.611992359161377, "learning_rate": 2.5622775800711747e-05, "loss": 0.855, "step": 980}, {"epoch": 3.516873889875666, "grad_norm": 4.139740943908691, "learning_rate": 2.3843416370106764e-05, "loss": 0.8559, "step": 990}, {"epoch": 3.552397868561279, "grad_norm": 4.1214680671691895, "learning_rate": 2.2064056939501783e-05, "loss": 0.8719, "step": 1000}, {"epoch": 3.587921847246892, "grad_norm": 3.8814144134521484, "learning_rate": 2.02846975088968e-05, "loss": 0.8376, "step": 1010}, {"epoch": 3.6234458259325044, "grad_norm": 3.9353978633880615, "learning_rate": 1.8505338078291815e-05, "loss": 0.8726, "step": 1020}, {"epoch": 3.6589698046181174, "grad_norm": 4.584665298461914, "learning_rate": 1.672597864768683e-05, "loss": 0.8642, "step": 1030}, {"epoch": 3.69449378330373, "grad_norm": 4.127754211425781, "learning_rate": 1.494661921708185e-05, "loss": 0.8981, "step": 1040}, {"epoch": 3.730017761989343, "grad_norm": 3.9699578285217285, "learning_rate": 1.3167259786476867e-05, "loss": 0.8465, "step": 1050}, {"epoch": 3.765541740674956, "grad_norm": 4.4796366691589355, "learning_rate": 1.1387900355871885e-05, "loss": 0.8508, "step": 1060}, {"epoch": 3.8010657193605684, "grad_norm": 3.9343647956848145, "learning_rate": 9.608540925266903e-06, "loss": 0.8558, "step": 1070}, {"epoch": 3.8365896980461813, "grad_norm": 4.792673587799072, "learning_rate": 7.829181494661921e-06, "loss": 0.8428, "step": 1080}, {"epoch": 3.872113676731794, "grad_norm": 4.515363693237305, "learning_rate": 6.04982206405694e-06, "loss": 0.8673, "step": 1090}, {"epoch": 3.907637655417407, "grad_norm": 4.963308334350586, "learning_rate": 4.270462633451958e-06, "loss": 0.8411, "step": 1100}, {"epoch": 3.94316163410302, "grad_norm": 4.601434707641602, "learning_rate": 2.491103202846975e-06, "loss": 0.8537, "step": 1110}, {"epoch": 3.9786856127886323, "grad_norm": 4.321945667266846, "learning_rate": 7.117437722419928e-07, "loss": 0.8439, "step": 1120}], "logging_steps": 10, "max_steps": 1124, "num_input_tokens_seen": 0, "num_train_epochs": 4, "save_steps": 100, "stateful_callbacks": {"TrainerControl": {"args": {"should_epoch_stop": false, "should_evaluate": false, "should_log": false, "should_save": true, "should_training_stop": true}, "attributes": {}}}, "total_flos": 7.72212266090496e+17, "train_batch_size": 16, "trial_name": null, "trial_params": null}