from transformers import AutoTokenizer, AutoModelForCausalLM
from peft import PeftModel, get_peft_model, LoraConfig, TaskType
from datasets import load_dataset
import torch
import torch.nn as nn
import torch.nn.functional as F
from torch.utils.data import DataLoader
from peft import prepare_model_for_kbit_training

# === Load base model and tokenizer ===
model_id = "mistralai/Mistral-7B-v0.1"
tokenizer = AutoTokenizer.from_pretrained(model_id)
tokenizer.pad_token = tokenizer.eos_token

base_model = AutoModelForCausalLM.from_pretrained(model_id, torch_dtype=torch.float16, device_map="auto")
base_model.eval()  # base is always frozen

# === Load frozen LoRA_1 ===
lora1_path = "/home/<USER>/m/maryam.hashemzadeh/scratch/saftly/mergoo/projects/loras/mistral-lora-drug_abuse"
model_with_lora1 = PeftModel.from_pretrained(base_model, lora1_path)
model_with_lora1.eval()  # freeze LoRA_1

# === Create trainable LoRA_2 ===
lora_config2 = LoraConfig(
    r=32, lora_alpha=64, target_modules=["up_proj", "down_proj", "gate_proj"], lora_dropout=0.1,
    bias="none", task_type=TaskType.CAUSAL_LM
)
model_with_lora2 = get_peft_model(base_model, lora_config2)
model_with_lora2.train()

# === Discriminator (to distinguish h1 vs. h2) === its output is a scalar
class Discriminator(nn.Module):
    def __init__(self, hidden_size):
        super().__init__()
        self.fc = nn.Sequential(
            nn.Linear(hidden_size, hidden_size),
            nn.ReLU(),
            nn.Linear(hidden_size, 1)
        )

    def forward(self, h):
        return self.fc(h)

discriminator = Discriminator(hidden_size=4096).cuda()

# === Dataset & Dataloader ===
# Option 1: Load from local JSON file
dataset_path = "/home/<USER>/m/maryam.hashemzadeh/scratch/saftly/MoE-PEFT/datasets/beavertail/4_cleaned_330k_train_beaverTails_safe_gpt4_qtype4_drug_abuse_weapons_banned_substance.json"
dataset = load_dataset("json", data_files=dataset_path)["train"]

# Option 2: Load HuggingFace dataset
# dataset = load_dataset("fwnlp/self-instruct-safety-alignment")["train"]
train_test = dataset.train_test_split(test_size=0.1, seed=42)
if len(train_test['train']) < 6000:
    train_dataset = train_test['train'].shuffle(seed=42).select(range(len(train_test['train'])))
else:
    train_dataset = train_test['train'].shuffle(seed=42).select(range(6000))
if len(train_test['test']) < 1000:
    eval_dataset = train_test['test'].shuffle(seed=42).select(range(len(train_test['test'])))
else:
    eval_dataset = train_test['test'].shuffle(seed=42).select(range(1000))

def formatting_func(example):
    # Determine which key to use
    question_key = "prompt" if "prompt" in example else "instruction"
    return [f"### Question: {q}\n### Answer: {a}" for q, a in zip(example[question_key], example["response"])]

# Apply formatting to datasets
train_dataset = train_dataset.map(lambda x: {"text": formatting_func(x)}, batched=False)
eval_dataset = eval_dataset.map(lambda x: {"text": formatting_func(x)}, batched=False)

def collate_fn(batch):
    inputs = tokenizer([x["text"] for x in batch], return_tensors="pt", padding=True, truncation=True)
    return inputs

dataloader = DataLoader(train_dataset, batch_size=4, shuffle=True, collate_fn=collate_fn)


# === Optimizers ===
optimizer_lora2 = torch.optim.AdamW(model_with_lora2.parameters(), lr=5e-5) #2e-4, 5e-5
optimizer_disc = torch.optim.AdamW(discriminator.parameters(), lr=1e-4)

# === Training Loop ===
kl_loss_fn = nn.KLDivLoss(reduction="batchmean")

for epoch in range(10):
    for batch in dataloader:
        input_ids = batch["input_ids"].cuda()
        attention_mask = batch["attention_mask"].cuda()

        with torch.no_grad():
            out_base = base_model(input_ids, attention_mask=attention_mask, output_hidden_states=True)
            out_lora1 = model_with_lora1(input_ids, attention_mask=attention_mask, output_hidden_states=True)

        out_lora2 = model_with_lora2(input_ids, attention_mask=attention_mask, output_hidden_states=True)

        # === Task: KL divergence from base logits ===
        logits_base = out_base.logits
        logits_lora2 = out_lora2.logits

        probs_base = F.log_softmax(logits_base, dim=-1)
        probs_lora2 = F.softmax(logits_lora2, dim=-1)

        kl_loss = kl_loss_fn(probs_lora2, probs_base)

        # === Adversarial loss ===
        # h1 = out_lora1.hidden_states[-1][:, 0, :]  # [CLS] or first token
        # h2 = out_lora2.hidden_states[-1][:, 0, :]

        # === Extract hidden states for adversarial loss (last non-pad token) ===
        h1_all = out_lora1.hidden_states[-1]
        h2_all = out_lora2.hidden_states[-1]

        last_token_indices = attention_mask.sum(dim=1) - 1  # [batch]
        h1 = h1_all[range(h1_all.size(0)), last_token_indices]  # [batch, hidden]
        h2 = h2_all[range(h2_all.size(0)), last_token_indices]

        # Train discriminator
        logits_disc_h1 = discriminator(h1.detach())
        logits_disc_h2 = discriminator(h2.detach())
        loss_disc = F.binary_cross_entropy_with_logits(logits_disc_h1, torch.zeros_like(logits_disc_h1)) + \
                    F.binary_cross_entropy_with_logits(logits_disc_h2, torch.ones_like(logits_disc_h2))

        optimizer_disc.zero_grad()
        loss_disc.backward()
        optimizer_disc.step()

        # Train LoRA₂ to fool discriminator
        logits_disc_h2_adv = discriminator(h2)
        adv_loss = F.binary_cross_entropy_with_logits(logits_disc_h2_adv, torch.zeros_like(logits_disc_h2_adv))  # want D to think h2 ≈ h1

        total_loss = kl_loss + 0.1 * adv_loss

        optimizer_lora2.zero_grad()
        total_loss.backward()
        optimizer_lora2.step()

        print(f"Epoch {epoch} | KL: {kl_loss.item():.4f} | ADV: {adv_loss.item():.4f} | D Loss: {loss_disc.item():.4f}")
